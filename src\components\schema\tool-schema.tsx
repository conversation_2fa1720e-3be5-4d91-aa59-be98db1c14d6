'use client'

import { useEffect } from 'react'
import { useTranslations, useLocale } from 'next-intl'
import { SITE_URL } from '@/lib/constants'

// Helper function to parse slug for schema
function parseSlugForSchema(slug: string): {
  inputFormat?: string
  outputFormat?: string
} {
  // Check if it's a format-to-format pattern
  const formatToFormatMatch = slug.match(/^(\w+)-to-(\w+)$/)
  if (formatToFormatMatch) {
    return {
      inputFormat: formatToFormatMatch[1],
      outputFormat: formatToFormatMatch[2],
    }
  }

  // Check if it's a format-a-format pattern (Spanish)
  const formatAFormatMatch = slug.match(/^(\w+)-a-(\w+)$/)
  if (formatAFormatMatch) {
    return {
      inputFormat: formatAFormatMatch[1],
      outputFormat: formatAFormatMatch[2],
    }
  }

  // Check if it's a single format converter
  const singleFormatMatch = slug.match(/^(\w+)-converter$/)
  if (singleFormatMatch) {
    return {
      inputFormat: singleFormatMatch[1],
      outputFormat: singleFormatMatch[1],
    }
  }

  return {}
}

interface ToolSchemaProps {
  slug: string
  rating?: number
  ratingCount?: number
}

export function ToolSchema({
  slug,
  rating = 4.5,
  ratingCount = 25,
}: ToolSchemaProps) {
  const t = useTranslations()
  const locale = useLocale()

  useEffect(() => {
    // Tool Schema
    const toolScript = document.createElement('script')
    toolScript.type = 'application/ld+json'
    toolScript.id = 'tool-schema'

    // Generate title and description based on the slug
    let toolName, toolDescription

    // For dynamic converter slugs, use the dynamicConverter translations
    const { inputFormat, outputFormat } = parseSlugForSchema(slug)
    if (inputFormat && outputFormat && inputFormat !== outputFormat) {
      toolName = `${inputFormat.toUpperCase()} to ${outputFormat.toUpperCase()} Converter`
      toolDescription = `Convert ${inputFormat.toUpperCase()} files to ${outputFormat.toUpperCase()} format`
    } else if (inputFormat) {
      toolName = `${inputFormat.toUpperCase()} Converter`
      toolDescription = `Convert ${inputFormat.toUpperCase()} files to other formats`
    } else {
      // Fallback
      toolName = slug
      toolDescription = `Convert ${slug} files`
    }

    const toolUrl = `${SITE_URL}/${locale}/${slug}`

    const toolSchema = {
      '@context': 'https://schema.org',
      '@type': 'SoftwareApplication',
      name: toolName,
      description: toolDescription,
      applicationCategory: 'MultimediaApplication',
      operatingSystem: 'Any',
      offers: {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'USD',
      },
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue: rating.toString(),
        ratingCount: ratingCount.toString(),
        bestRating: '5',
        worstRating: '1',
      },
      url: toolUrl,
    }

    toolScript.innerHTML = JSON.stringify(toolSchema)
    document.head.appendChild(toolScript)

    // Breadcrumb Schema
    const breadcrumbScript = document.createElement('script')
    breadcrumbScript.type = 'application/ld+json'
    breadcrumbScript.id = 'breadcrumb-schema'

    const breadcrumbSchema = {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: t('nav.home'),
          item: `${SITE_URL}/${locale}`,
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: t('nav.tools'),
          item: `${SITE_URL}/${locale}/tools`,
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: toolName,
          item: `${SITE_URL}/${locale}/${slug}`,
        },
      ],
    }

    breadcrumbScript.innerHTML = JSON.stringify(breadcrumbSchema)
    document.head.appendChild(breadcrumbScript)

    return () => {
      const existingToolScript = document.getElementById('tool-schema')
      if (existingToolScript) {
        existingToolScript.remove()
      }

      const existingBreadcrumbScript =
        document.getElementById('breadcrumb-schema')
      if (existingBreadcrumbScript) {
        existingBreadcrumbScript.remove()
      }
    }
  }, [slug, t, locale, rating, ratingCount])

  return null
}
