import { MetadataRoute } from 'next'
import { TOOLS, SITE_URL, LOCALES } from '@/lib/constants'

/**
 * Generate a dynamic sitemap for the application
 * This will include all routes with proper localization
 */
export default function sitemap(): MetadataRoute.Sitemap {
  const currentDate = new Date()
  const sitemapEntries: MetadataRoute.Sitemap = []

  // Add root page that redirects to default locale
  sitemapEntries.push({
    url: SITE_URL,
    lastModified: currentDate,
    changeFrequency: 'daily',
    priority: 1.0,
  })

  // Add home page for each locale
  LOCALES.forEach((locale) => {
    sitemapEntries.push({
      url: `${SITE_URL}/${locale}`,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 1.0,
    })
  })

  // Add tools index page for each locale
  LOCALES.forEach((locale) => {
    sitemapEntries.push({
      url: `${SITE_URL}/${locale}/tools`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    })
  })

  // Add individual tool pages for each locale
  TOOLS.forEach((tool) => {
    LOCALES.forEach((locale) => {
      sitemapEntries.push({
        url: `${SITE_URL}/${locale}/tools/${tool.slug}`,
        lastModified: currentDate,
        changeFrequency: 'weekly',
        priority: 0.8,
      })
    })
  })

  return sitemapEntries
}
