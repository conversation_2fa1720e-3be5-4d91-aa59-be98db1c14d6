'use client'
import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile, toBlobURL } from '@ffmpeg/util'
import { useRef, useState, useEffect } from 'react'
import { useTranslations, useLocale } from 'next-intl'
import { useRouter } from '@/i18n/navigation'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  getFFmpegCommand,
  getMimeType,
  detectFormatFromFileName,
} from '@/lib/video-formats'
import {
  getAllFormats,
  getValidOutputFormats,
  generateFormatSlug,
  isAudioFormat,
  getFormatName,
  getFormatDescription,
} from '@/lib/format-utils'
import {
  extractVideoMetadata,
  generateResolutionOptions,
  generateQualityOptions,
  generateFpsOptions,
  generateAudioBitrateOptions,
  applySettingsToFFmpegCommand,
  generateAspectRatioOptions,
  generateScalingMethodOptions,
  generateKeyframeIntervalOptions,
  generateVideoCodecOptions,
  generateAudioCodecOptions,
  generateAudioChannelOptions,
  generateAudioSampleRateOptions,
  generateAudioVolumeOptions,
} from '@/lib/video-metadata'

interface DynamicFormatConverterProps {
  slug: string
  inputFormat: string
  outputFormat: string
  isSingleFormat: boolean
}

export const DynamicFormatConverter = ({
  inputFormat,
  outputFormat,
}: DynamicFormatConverterProps) => {
  const t = useTranslations()
  const locale = useLocale()
  const router = useRouter()

  const [ffmpegLoaded, setFFmpegLoaded] = useState(false)
  const [isFFmpegLoading, setIsFFmpegLoading] = useState(false)
  const [isConverting, setIsConverting] = useState(false)
  const [progress, setProgress] = useState(0)
  const [file, setFile] = useState<File | null>(null)
  const [outputUrl, setOutputUrl] = useState<string | null>(null)
  const [selectedInputFormat, setSelectedInputFormat] = useState(inputFormat)
  const [selectedOutputFormat, setSelectedOutputFormat] = useState(outputFormat)
  const [detectedFormat, setDetectedFormat] = useState<string | null>(null)
  const [validOutputFormats, setValidOutputFormats] = useState<string[]>([])

  // Video metadata state
  const [videoMetadata, setVideoMetadata] = useState<{
    width: number
    height: number
    duration: number
    fps?: number
  } | null>(null)

  // Settings state
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false)

  // Video settings
  const [resolutionOptions, setResolutionOptions] = useState<
    { label: string; width: number; height: number }[]
  >([])
  const [selectedResolution, setSelectedResolution] =
    useState<string>('original')
  const [quality, setQuality] = useState<number>(23) // Default CRF value
  const [fps, setFps] = useState<number | null>(null)

  // Custom resolution
  const [useCustomResolution, setUseCustomResolution] = useState<boolean>(false)
  const [customWidth, setCustomWidth] = useState<number>(1920)
  const [customHeight, setCustomHeight] = useState<number>(1080)

  // Aspect ratio and scaling
  const [aspectRatio, setAspectRatio] = useState<string>('original')
  const [scalingMethod, setScalingMethod] = useState<string>('fit')

  // Advanced video settings
  const [keyframeInterval, setKeyframeInterval] = useState<number>(0) // 0 = auto
  const [videoCodec, setVideoCodec] = useState<string>('auto')

  // Audio settings
  const [audioBitrate, setAudioBitrate] = useState<string>('128k')
  const [audioCodec, setAudioCodec] = useState<string>('auto')
  const [audioChannels, setAudioChannels] = useState<number>(0) // 0 = auto
  const [audioSampleRate, setAudioSampleRate] = useState<number>(0) // 0 = auto
  const [audioVolume, setAudioVolume] = useState<number>(1.0) // 1.0 = original volume

  // Create FFmpeg instance
  const ffmpegRef = useRef(new FFmpeg())
  const fileInputRef = useRef<HTMLInputElement | null>(null)
  const messageRef = useRef<HTMLParagraphElement | null>(null)
  const messageContainerRef = useRef<HTMLDivElement | null>(null)

  // Update valid output formats when input format changes
  useEffect(() => {
    setValidOutputFormats(getValidOutputFormats(selectedInputFormat))
  }, [selectedInputFormat])

  // Handle input format change
  const handleInputFormatChange = (value: string) => {
    setSelectedInputFormat(value)

    // If the new input format is audio, ensure the output format is also audio
    if (isAudioFormat(value) && !isAudioFormat(selectedOutputFormat)) {
      // Set output format to the first valid audio format
      const audioFormats = getValidOutputFormats(value)
      setSelectedOutputFormat(audioFormats[0])

      // Navigate to the new URL
      const newSlug = generateFormatSlug(value, audioFormats[0], locale)
      router.push(`/${newSlug}`)
    } else {
      // Navigate to the new URL
      const newSlug = generateFormatSlug(value, selectedOutputFormat, locale)
      router.push(`/${newSlug}`)
    }
  }

  // Handle output format change
  const handleOutputFormatChange = (value: string) => {
    setSelectedOutputFormat(value)

    // Navigate to the new URL
    const newSlug = generateFormatSlug(selectedInputFormat, value, locale)
    router.push(`/${newSlug}`)
  }

  // Check if file format matches the selected input format
  const checkFileFormat = (file: File): boolean => {
    // Detect format from file name
    const detectedFormat = detectFormatFromFileName(file.name)

    // If we couldn't detect the format, allow the file (we'll handle this later)
    if (!detectedFormat) return true

    // Check if the detected format matches the selected input format
    if (detectedFormat !== selectedInputFormat) {
      if (messageRef.current && messageContainerRef.current) {
        messageRef.current.innerHTML = `Invalid file format. Please select a ${selectedInputFormat.toUpperCase()} file or change the input format.`
        // Make the message visible
        messageContainerRef.current.classList.remove('hidden')
        messageContainerRef.current.classList.add(
          'bg-red-50',
          'p-4',
          'rounded-md',
          'border',
          'border-red-200',
          'mb-4'
        )
      }
      return false
    }

    return true
  }

  // Check file size
  const checkFileSize = (file: File): boolean => {
    const fileSizeMB = file.size / (1024 * 1024)

    // Reject files larger than 500MB
    if (fileSizeMB > 500) {
      if (messageRef.current && messageContainerRef.current) {
        messageRef.current.innerHTML =
          'File is too large. Please select a file smaller than 500MB.'
        // Make the message visible
        messageContainerRef.current.classList.remove('hidden')
        messageContainerRef.current.classList.add(
          'bg-red-50',
          'p-4',
          'rounded-md',
          'border',
          'border-red-200',
          'mb-4'
        )
      }
      return false
    }

    // Show warning for files larger than 100MB
    if (fileSizeMB > 100) {
      if (messageRef.current && messageContainerRef.current) {
        messageRef.current.innerHTML =
          'Warning: Large file detected. Processing may take longer and require more memory. Consider reducing the resolution in settings for better performance.'
        // Make the message visible
        messageContainerRef.current.classList.remove('hidden')
        messageContainerRef.current.classList.add(
          'bg-yellow-50',
          'p-4',
          'rounded-md',
          'border',
          'border-yellow-200',
          'mb-4'
        )
      }
    }

    return true
  }

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      // Clear previous messages
      if (messageRef.current && messageContainerRef.current) {
        messageContainerRef.current.classList.add('hidden')
        messageRef.current.innerHTML = ''
      }

      // Check file format first
      if (!checkFileFormat(selectedFile)) {
        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = ''
        }
        return
      }

      // Then check file size
      if (checkFileSize(selectedFile)) {
        setFile(selectedFile)
        setOutputUrl(null)

        // Detect format from file name
        const format = detectFormatFromFileName(selectedFile.name)
        setDetectedFormat(format)

        // Extract video metadata if it's a video file
        if (!isAudioFormat(format || '')) {
          try {
            const metadata = await extractVideoMetadata(selectedFile)
            setVideoMetadata(metadata)

            // Generate resolution options based on the original video size
            const options = generateResolutionOptions(
              metadata.width,
              metadata.height
            )
            setResolutionOptions(options)

            // Set FPS if available
            if (metadata.fps) {
              setFps(metadata.fps)
            }
          } catch (error) {
            console.error('Failed to extract video metadata:', error)
          }
        } else {
          // Reset video metadata for audio files
          setVideoMetadata(null)
          setResolutionOptions([])
          setFps(null)
        }
      } else {
        // Reset file input if size check fails
        if (fileInputRef.current) {
          fileInputRef.current.value = ''
        }
      }
    }
  }

  const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    const droppedFile = event.dataTransfer.files[0]
    if (droppedFile) {
      // Clear previous messages
      if (messageRef.current && messageContainerRef.current) {
        messageContainerRef.current.classList.add('hidden')
        messageRef.current.innerHTML = ''
      }

      // Check file format first
      if (!checkFileFormat(droppedFile)) {
        return
      }

      // Then check file size
      if (checkFileSize(droppedFile)) {
        setFile(droppedFile)
        setOutputUrl(null)

        // Detect format from file name
        const format = detectFormatFromFileName(droppedFile.name)
        setDetectedFormat(format)

        // Extract video metadata if it's a video file
        if (!isAudioFormat(format || '')) {
          try {
            const metadata = await extractVideoMetadata(droppedFile)
            setVideoMetadata(metadata)

            // Generate resolution options based on the original video size
            const options = generateResolutionOptions(
              metadata.width,
              metadata.height
            )
            setResolutionOptions(options)

            // Set FPS if available
            if (metadata.fps) {
              setFps(metadata.fps)
            }
          } catch (error) {
            console.error('Failed to extract video metadata:', error)
          }
        } else {
          // Reset video metadata for audio files
          setVideoMetadata(null)
          setResolutionOptions([])
          setFps(null)
        }
      }
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
  }

  const convertFile = async () => {
    if (!file || !detectedFormat) return

    try {
      setIsConverting(true)
      setProgress(0)
      setOutputUrl(null)

      // Load FFmpeg if not already loaded
      if (!ffmpegLoaded) {
        setIsFFmpegLoading(true)
        const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.10/dist/umd'
        const ffmpeg = ffmpegRef.current

        // Set up logging
        ffmpeg.on('log', ({ message }) => {
          if (messageRef.current) messageRef.current.innerHTML = message
          // console.log(message)
        })

        // Set up progress tracking
        ffmpeg.on('progress', ({ progress }) => {
          setProgress(Math.min(100, Math.round(progress * 100)))
        })

        // toBlobURL is used to bypass CORS issue
        await ffmpeg.load({
          coreURL: await toBlobURL(
            `${baseURL}/ffmpeg-core.js`,
            'text/javascript'
          ),
          wasmURL: await toBlobURL(
            `${baseURL}/ffmpeg-core.wasm`,
            'application/wasm'
          ),
        })

        setFFmpegLoaded(true)
        setIsFFmpegLoading(false)
      }

      const ffmpeg = ffmpegRef.current

      // Check file size and apply appropriate memory optimizations
      const fileSizeMB = file.size / (1024 * 1024)

      // Determine memory optimization level based on file size
      // - Level 0: No optimization needed (small files)
      // - Level 1: Basic optimization (medium files)
      // - Level 2: Aggressive optimization (large files)
      // - Level 3: Maximum optimization (very large files)
      let memoryOptimizationLevel = 0
      if (fileSizeMB > 300) {
        memoryOptimizationLevel = 3 // Maximum optimization for very large files
      } else if (fileSizeMB > 150) {
        memoryOptimizationLevel = 2 // Aggressive optimization for large files
      } else if (fileSizeMB > 50) {
        memoryOptimizationLevel = 1 // Basic optimization for medium files
      }

      // Show appropriate warnings based on file size
      if (messageRef.current && messageContainerRef.current) {
        if (fileSizeMB > 300) {
          // Very large file warning
          messageRef.current.innerHTML =
            'Warning: Very large file detected. Processing may fail due to browser memory limitations. ' +
            'Consider using a smaller file or reducing resolution and quality settings significantly.'
          messageContainerRef.current.classList.remove('hidden')
          messageContainerRef.current.classList.add(
            'bg-red-50',
            'p-4',
            'rounded-md',
            'border',
            'border-red-200',
            'mb-4'
          )
        } else if (fileSizeMB > 150) {
          // Large file warning
          messageRef.current.innerHTML =
            'Warning: Large file detected. Processing may take longer or fail due to memory constraints. ' +
            'Consider reducing resolution or quality in settings.'
          messageContainerRef.current.classList.remove('hidden')
          messageContainerRef.current.classList.add(
            'bg-yellow-50',
            'p-4',
            'rounded-md',
            'border',
            'border-yellow-200',
            'mb-4'
          )
        }
      }

      const inputFileName = `input.${detectedFormat}`
      const outputFileName = `output.${selectedOutputFormat}`

      try {
        // Write the input file to FFmpeg's virtual file system
        await ffmpeg.writeFile(inputFileName, await fetchFile(file))

        // Get the base FFmpeg command for the conversion
        let ffmpegCommand = getFFmpegCommand(
          detectedFormat,
          selectedOutputFormat
        )

        // Create settings object with all parameters
        const conversionSettings = {
          outputFormat: selectedOutputFormat,
          optimizeMemory: memoryOptimizationLevel > 0,
        }

        // Apply resolution setting if not original
        if (selectedResolution !== 'original' && videoMetadata) {
          // Parse the resolution string to get width and height
          const [width, height] = selectedResolution.split('x').map(Number)

          // For large files, consider downscaling to reduce memory usage
          // Apply more aggressive downscaling based on memory optimization level
          let maxWidth = 1920 // Default max width
          if (memoryOptimizationLevel >= 3) {
            maxWidth = 854 // 480p for very large files
          } else if (memoryOptimizationLevel >= 2) {
            maxWidth = 1280 // 720p for large files
          } else if (memoryOptimizationLevel >= 1) {
            maxWidth = 1920 // 1080p for medium files
          }

          if (memoryOptimizationLevel > 0 && width > maxWidth) {
            // Limit resolution for large files to reduce memory usage
            const aspectRatio = width / height
            const newWidth = Math.min(width, maxWidth)
            const newHeight = Math.round(newWidth / aspectRatio)

            ffmpegCommand = applySettingsToFFmpegCommand(ffmpegCommand, {
              ...conversionSettings,
              resolution: { width: newWidth, height: newHeight },
            })

            // Show a message about resolution reduction
            if (messageRef.current && messageContainerRef.current) {
              messageRef.current.innerHTML +=
                '<br>Resolution automatically reduced to improve performance. Original quality may not be preserved.'
            }
          } else {
            // Use requested resolution
            ffmpegCommand = applySettingsToFFmpegCommand(ffmpegCommand, {
              ...conversionSettings,
              resolution: { width, height },
            })
          }
        } else {
          // Apply memory optimization without changing resolution
          ffmpegCommand = applySettingsToFFmpegCommand(
            ffmpegCommand,
            conversionSettings
          )
        }

        // Apply quality setting (use higher CRF/lower quality for large files to reduce memory usage)
        // Adjust quality based on memory optimization level
        let adjustedQuality = quality
        if (memoryOptimizationLevel >= 3) {
          // Maximum optimization - increase CRF by 8 (much lower quality)
          adjustedQuality = Math.min(quality + 8, 35)
        } else if (memoryOptimizationLevel >= 2) {
          // Aggressive optimization - increase CRF by 5 (lower quality)
          adjustedQuality = Math.min(quality + 5, 35)
        } else if (memoryOptimizationLevel >= 1) {
          // Basic optimization - increase CRF by 2 (slightly lower quality)
          adjustedQuality = Math.min(quality + 2, 35)
        }

        // Create a complete settings object with all parameters
        const completeSettings = {
          ...conversionSettings,
          quality: adjustedQuality,
          fps: fps !== null ? fps : undefined,
          audioBitrate,

          // Add custom resolution if enabled
          customResolution: useCustomResolution
            ? { width: customWidth, height: customHeight }
            : undefined,

          // Add aspect ratio and scaling method
          aspectRatio,
          scalingMethod,

          // Add advanced video settings
          keyframeInterval: keyframeInterval > 0 ? keyframeInterval : undefined,
          videoCodec: videoCodec !== 'auto' ? videoCodec : undefined,

          // Add advanced audio settings
          audioCodec: audioCodec !== 'auto' ? audioCodec : undefined,
          audioChannels: audioChannels > 0 ? audioChannels : undefined,
          audioSampleRate: audioSampleRate > 0 ? audioSampleRate : undefined,
          audioVolume: audioVolume !== 1.0 ? audioVolume : undefined,
        }

        // Apply all settings at once
        ffmpegCommand = applySettingsToFFmpegCommand(
          ffmpegCommand,
          completeSettings
        )

        // console.log('Running FFmpeg with command:', ffmpegCommand)

        // Execute the FFmpeg command
        await ffmpeg.exec(ffmpegCommand)

        // Read the output file
        const data = await ffmpeg.readFile(outputFileName)

        // Create a download URL for the output file
        const url = URL.createObjectURL(
          new Blob([data instanceof Uint8Array ? data : data], {
            type: getMimeType(selectedOutputFormat),
          })
        )

        setOutputUrl(url)
      } catch (error) {
        console.error('FFmpeg execution error:', error)

        // Check if it's a memory error
        const errorMessage =
          error instanceof Error ? error.message : String(error)
        const isMemoryError =
          errorMessage.includes('memory') ||
          errorMessage.includes('allocation') ||
          errorMessage.includes('out of bounds') ||
          errorMessage.includes('heap') ||
          errorMessage.includes('stack') ||
          errorMessage.includes('overflow')

        console.error('Conversion error details:', {
          message: errorMessage,
          isMemoryError,
          fileSize: fileSizeMB,
          memoryOptimizationLevel,
          inputFormat: detectedFormat,
          outputFormat: selectedOutputFormat,
          resolution: selectedResolution,
        })

        if (messageRef.current && messageContainerRef.current) {
          if (isMemoryError) {
            // Provide specific guidance for memory errors based on file size
            if (fileSizeMB > 200) {
              messageRef.current.innerHTML =
                '<strong>Browser memory limit reached.</strong><br>' +
                'Your file is too large for browser-based conversion. Please try:<br>' +
                '1. Use a much smaller file (under 100MB)<br>' +
                '2. Try a desktop application instead<br>' +
                '3. Split your file into smaller segments before uploading'
            } else {
              messageRef.current.innerHTML =
                '<strong>Memory error during conversion.</strong><br>' +
                'Please try the following:<br>' +
                '1. Select a much lower resolution (480p or less)<br>' +
                '2. Choose the lowest quality setting<br>' +
                '3. Use a smaller input file<br>' +
                '4. Try a different output format (MP4 usually works best)<br>' +
                '5. Close other browser tabs to free up memory'
            }
          } else {
            // Generic error message for other errors
            messageRef.current.innerHTML =
              'Conversion failed. Please try a different format or a smaller file.'
          }

          // Make the message visible
          messageContainerRef.current.classList.remove('hidden')
          messageContainerRef.current.classList.add(
            'bg-red-50',
            'p-4',
            'rounded-md',
            'border',
            'border-red-200',
            'mb-4'
          )
        }

        // Clean up any partial output and release memory
        try {
          if (ffmpegLoaded) {
            // Delete output file if it exists
            ffmpeg.deleteFile(`output.${selectedOutputFormat}`).catch(() => {
              // Ignore errors during cleanup
            })

            // Delete input file to free up memory
            ffmpeg.deleteFile(`input.${detectedFormat}`).catch(() => {
              // Ignore errors during cleanup
            })

            // Force garbage collection if possible (not directly possible in JavaScript,
            // but we can remove references to large objects)
            if (isMemoryError) {
              // In case of memory error, try to release as much memory as possible
              setFile(null)
              if (fileInputRef.current) {
                fileInputRef.current.value = ''
              }
            }
          }
        } catch (cleanupError) {
          console.error('Error during cleanup:', cleanupError)
        }
      }
    } catch (error) {
      console.error('Conversion error:', error)
      if (messageRef.current) {
        messageRef.current.innerHTML =
          'An error occurred. Please try again or use a smaller file.'
        // Make the message visible
        if (messageContainerRef.current) {
          messageContainerRef.current.classList.remove('hidden')
          messageContainerRef.current.classList.add(
            'bg-red-50',
            'p-4',
            'rounded-md',
            'border',
            'border-red-200',
            'mb-4'
          )
        }
      }
    } finally {
      setIsConverting(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Format Selection */}
      <Card>
        <CardContent className="">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            {/* Input Format Selection */}
            <div>
              <label className="mb-2 block text-sm font-medium">
                {t('dynamicConverter.inputFormatLabel')}
              </label>
              <Select
                value={selectedInputFormat}
                onValueChange={handleInputFormatChange}
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={t('dynamicConverter.selectInputFormat')}
                  />
                </SelectTrigger>
                <SelectContent className="max-h-[300px] overflow-y-auto">
                  {getAllFormats().map((format) => (
                    <SelectItem key={format} value={format}>
                      {getFormatName(format)} (.{format}) -{' '}
                      {getFormatDescription(format)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Output Format Selection */}
            <div>
              <label className="mb-2 block text-sm font-medium">
                {t('dynamicConverter.outputFormatLabel')}
              </label>
              <Select
                value={selectedOutputFormat}
                onValueChange={handleOutputFormatChange}
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={t('dynamicConverter.selectOutputFormat')}
                  />
                </SelectTrigger>
                <SelectContent className="max-h-[300px] overflow-y-auto">
                  {validOutputFormats.map((format) => (
                    <SelectItem key={format} value={format}>
                      {getFormatName(format)} (.{format}) -{' '}
                      {getFormatDescription(format)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* File Upload Area */}
      <Card>
        <CardContent className="">
          {/* Error/Warning Message Area */}
          <div className="mb-4 hidden" ref={messageContainerRef}>
            <p ref={messageRef} className="text-background text-sm"></p>
          </div>

          {!file ? (
            <div
              className="border-muted rounded-lg border-2 border-dashed p-8 text-center"
              onDrop={handleDrop}
              onDragOver={handleDragOver}
            >
              <div className="flex flex-col items-center justify-center space-y-4">
                <div className="bg-muted rounded-full p-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-muted-foreground"
                  >
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="17 8 12 3 7 8"></polyline>
                    <line x1="12" y1="3" x2="12" y2="15"></line>
                  </svg>
                </div>
                <div>
                  <p className="text-muted-foreground text-sm">
                    {t('tools.common.dropzone')}
                  </p>
                  <p className="text-muted-foreground mt-1 text-sm">
                    {t('dynamicConverter.expectedFormat', {
                      format: selectedInputFormat.toUpperCase(),
                    })}
                  </p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2"
                  >
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="17 8 12 3 7 8"></polyline>
                    <line x1="12" y1="3" x2="12" y2="15"></line>
                  </svg>
                  {t('dynamicConverter.selectFile')} (
                  {selectedInputFormat.toUpperCase()})
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept={`.${selectedInputFormat}`}
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* File List Item */}
              <div className="bg-muted/20 flex items-center rounded-md border p-3">
                <div className="flex-1 overflow-hidden">
                  <p className="truncate font-medium">{file.name}</p>
                  <p className="text-muted-foreground text-xs">
                    {(file.size / (1024 * 1024)).toFixed(2)} MB
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="ml-2"
                  onClick={() => {
                    setFile(null)
                    setOutputUrl(null)
                    setDetectedFormat(null)
                    if (fileInputRef.current) {
                      fileInputRef.current.value = ''
                    }
                  }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M18 6L6 18M6 6l12 12" />
                  </svg>
                  <span className="sr-only">Remove</span>
                </Button>
              </div>

              {/* Convert Button */}
              <div className="flex justify-center">
                <Button
                  onClick={convertFile}
                  disabled={isConverting || isFFmpegLoading || !detectedFormat}
                >
                  {isConverting || isFFmpegLoading ? (
                    <>
                      <span className="mr-2 animate-spin">
                        <svg
                          viewBox="0 0 1024 1024"
                          focusable="false"
                          data-icon="loading"
                          width="1em"
                          height="1em"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path>
                        </svg>
                      </span>
                      {isFFmpegLoading
                        ? t('tools.common.ffmpegLoading')
                        : t('tools.common.processing')}
                    </>
                  ) : (
                    t('tools.common.convert')
                  )}
                </Button>
              </div>

              {isConverting && (
                <div className="mx-auto max-w-xs">
                  <Progress value={progress} className="mb-2 h-2" />
                  <p className="text-center text-xs">{progress}%</p>
                </div>
              )}

              {outputUrl && (
                <div className="flex justify-center">
                  <Button asChild>
                    <a
                      href={outputUrl}
                      download={`converted.${selectedOutputFormat}`}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="mr-2"
                      >
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7 10 12 15 17 10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                      </svg>
                      {t('tools.common.download')}
                    </a>
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Settings Area - Only show when a file is uploaded */}
      {file && !isAudioFormat(detectedFormat || '') && (
        <Card>
          <CardContent className="">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-medium">
                {t('dynamicConverter.settings')}
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
              >
                {showAdvancedSettings
                  ? t('dynamicConverter.hideAdvancedSettings')
                  : t('dynamicConverter.showAdvancedSettings')}
              </Button>
            </div>

            <div className="space-y-6">
              {/* Video Settings */}
              <div>
                <h4 className="mb-4 text-sm font-medium">
                  {t('dynamicConverter.videoSettings')}
                </h4>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {/* Resolution Setting */}
                  <div>
                    <label className="mb-2 block text-sm font-medium">
                      {t('dynamicConverter.resolution')}
                    </label>
                    <Select
                      value={selectedResolution}
                      onValueChange={setSelectedResolution}
                      disabled={useCustomResolution}
                    >
                      <SelectTrigger>
                        <SelectValue
                          placeholder={t('dynamicConverter.original')}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="original">
                          {t('dynamicConverter.original')}{' '}
                          {videoMetadata
                            ? `(${videoMetadata.width}x${videoMetadata.height})`
                            : ''}
                        </SelectItem>
                        {resolutionOptions.map((option) => (
                          <SelectItem key={option.label} value={option.label}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Custom Resolution */}
                  <div>
                    <div className="mb-2 flex items-center space-x-2">
                      <Checkbox
                        id="useCustomResolution"
                        checked={useCustomResolution}
                        onCheckedChange={(checked: boolean) => {
                          setUseCustomResolution(checked)
                        }}
                      />
                      <label
                        className="text-sm font-medium"
                        htmlFor="useCustomResolution"
                      >
                        {t('dynamicConverter.useCustomResolution')}
                      </label>
                    </div>

                    {useCustomResolution && (
                      <div className="mt-2 grid grid-cols-2 gap-2">
                        <div>
                          <label className="mb-1 block text-xs">
                            {t('dynamicConverter.width')}
                          </label>
                          <Input
                            id="customWidth"
                            type="number"
                            value={customWidth}
                            onChange={(e) =>
                              setCustomWidth(parseInt(e.target.value) || 0)
                            }
                            min="16"
                            max="7680"
                          />
                        </div>
                        <div>
                          <label className="mb-1 block text-xs">
                            {t('dynamicConverter.height')}
                          </label>
                          <Input
                            id="customHeight"
                            type="number"
                            value={customHeight}
                            onChange={(e) =>
                              setCustomHeight(parseInt(e.target.value) || 0)
                            }
                            min="16"
                            max="4320"
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Aspect Ratio */}
                  <div>
                    <label className="mb-2 block text-sm font-medium">
                      {t('dynamicConverter.aspectRatio')}
                    </label>
                    <Select value={aspectRatio} onValueChange={setAspectRatio}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {generateAspectRatioOptions().map((option) => (
                          <SelectItem key={option.label} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Scaling Method */}
                  {aspectRatio !== 'original' && (
                    <div>
                      <label className="mb-2 block text-sm font-medium">
                        {t('dynamicConverter.scalingMethod')}
                      </label>
                      <Select
                        value={scalingMethod}
                        onValueChange={setScalingMethod}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {generateScalingMethodOptions().map((option) => (
                            <SelectItem key={option.label} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* Quality Setting */}
                  <div>
                    <label className="mb-2 block text-sm font-medium">
                      {t('dynamicConverter.quality')}
                    </label>
                    <Select
                      value={quality.toString()}
                      onValueChange={(value) => setQuality(Number(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {generateQualityOptions().map((option) => (
                          <SelectItem
                            key={option.value}
                            value={option.value.toString()}
                          >
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* FPS Setting */}
                  <div>
                    <label className="mb-2 block text-sm font-medium">
                      {t('dynamicConverter.fps')}
                    </label>
                    <Select
                      value={fps?.toString() || 'original'}
                      onValueChange={(value) =>
                        setFps(value === 'original' ? null : Number(value))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue
                          placeholder={t('dynamicConverter.original')}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="original">
                          {t('dynamicConverter.original')}
                        </SelectItem>
                        {generateFpsOptions().map((option) => (
                          <SelectItem
                            key={option.value}
                            value={option.value.toString()}
                          >
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Advanced Video Settings */}
                {showAdvancedSettings && (
                  <div className="mt-4 border-t pt-4">
                    <h4 className="mb-4 text-sm font-medium">
                      {t('dynamicConverter.advancedVideoSettings')}
                    </h4>
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                      {/* Video Codec */}
                      <div>
                        <label className="mb-2 block text-sm font-medium">
                          {t('dynamicConverter.videoCodec')}
                        </label>
                        <Select
                          value={videoCodec}
                          onValueChange={setVideoCodec}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {generateVideoCodecOptions().map((option) => (
                              <SelectItem
                                key={option.label}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Keyframe Interval */}
                      <div>
                        <label className="mb-2 block text-sm font-medium">
                          {t('dynamicConverter.keyframeInterval')}
                        </label>
                        <Select
                          value={keyframeInterval.toString()}
                          onValueChange={(value) =>
                            setKeyframeInterval(parseInt(value))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {generateKeyframeIntervalOptions().map((option) => (
                              <SelectItem
                                key={option.label}
                                value={option.value.toString()}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Audio Settings */}
              <div>
                <h4 className="mb-4 text-sm font-medium">
                  {t('dynamicConverter.audioSettings')}
                </h4>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {/* Audio Bitrate Setting */}
                  <div>
                    <label className="mb-2 block text-sm font-medium">
                      {t('dynamicConverter.audioBitrate')}
                    </label>
                    <Select
                      value={audioBitrate}
                      onValueChange={setAudioBitrate}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {generateAudioBitrateOptions().map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Audio Volume */}
                  <div>
                    <label className="mb-2 block text-sm font-medium">
                      {t('dynamicConverter.audioVolume')}
                    </label>
                    <Select
                      value={audioVolume.toString()}
                      onValueChange={(value) =>
                        setAudioVolume(parseFloat(value))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {generateAudioVolumeOptions().map((option) => (
                          <SelectItem
                            key={option.label}
                            value={option.value.toString()}
                          >
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Advanced Audio Settings */}
                {showAdvancedSettings && (
                  <div className="mt-4 border-t pt-4">
                    <h4 className="mb-4 text-sm font-medium">
                      {t('dynamicConverter.advancedAudioSettings')}
                    </h4>
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                      {/* Audio Codec */}
                      <div>
                        <label className="mb-2 block text-sm font-medium">
                          {t('dynamicConverter.audioCodec')}
                        </label>
                        <Select
                          value={audioCodec}
                          onValueChange={setAudioCodec}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {generateAudioCodecOptions().map((option) => (
                              <SelectItem
                                key={option.label}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Audio Channels */}
                      <div>
                        <label className="mb-2 block text-sm font-medium">
                          {t('dynamicConverter.audioChannels')}
                        </label>
                        <Select
                          value={audioChannels.toString()}
                          onValueChange={(value) =>
                            setAudioChannels(parseInt(value))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {generateAudioChannelOptions().map((option) => (
                              <SelectItem
                                key={option.label}
                                value={option.value.toString()}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Audio Sample Rate */}
                      <div>
                        <label className="mb-2 block text-sm font-medium">
                          {t('dynamicConverter.audioSampleRate')}
                        </label>
                        <Select
                          value={audioSampleRate.toString()}
                          onValueChange={(value) =>
                            setAudioSampleRate(parseInt(value))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {generateAudioSampleRateOptions().map((option) => (
                              <SelectItem
                                key={option.label}
                                value={option.value.toString()}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Reset Settings Button */}
              <div className="flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Reset video settings
                    setSelectedResolution('original')
                    setQuality(23)
                    setFps(null)

                    // Reset custom resolution
                    setUseCustomResolution(false)
                    setCustomWidth(1920)
                    setCustomHeight(1080)

                    // Reset aspect ratio and scaling
                    setAspectRatio('original')
                    setScalingMethod('fit')

                    // Reset advanced video settings
                    setKeyframeInterval(0)
                    setVideoCodec('auto')

                    // Reset audio settings
                    setAudioBitrate('128k')
                    setAudioCodec('auto')
                    setAudioChannels(0)
                    setAudioSampleRate(0)
                    setAudioVolume(1.0)
                  }}
                >
                  {t('dynamicConverter.resetSettings')}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
