import { getTranslations, setRequestLocale } from 'next-intl/server'
import { Link } from '@/i18n/navigation'
import { ChevronRight } from 'lucide-react'
import { routing } from '@/i18n/routing'

import { EnhancedToolSchema } from '@/components/schema/enhanced-tool-schema'
import { UniversalFormatConverter } from '@/components/universal-format-converter'
import { APP_NAME, SITE_URL, ToolSlugs } from '@/lib/constants'
import NoSSRWrapper from '@/components/no-ssr-wrapper'
import { RelatedToolsSidebar } from '@/components/related-tools-sidebar'
import { MarkdownContent } from '@/components/markdown-content'
import { getToolMarkdown } from '@/lib/markdown'

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }))
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params
  const t = await getTranslations({ locale, namespace: 'tools' })

  const toolName = t('universal-format-converter.title')
  const toolDescription = t('universal-format-converter.description')

  return {
    title: toolName,
    description: t('universal-format-converter.description'),
    alternates: {
      canonical: `/${locale}/tools/universal-format-converter`,
      languages: Object.fromEntries(
        routing.locales.map((l) => [
          l,
          `/${l}/tools/universal-format-converter`,
        ])
      ),
    },
    openGraph: {
      type: 'website',
      title: `${toolName} | ${APP_NAME}`,
      description: toolDescription,
      url: `${SITE_URL}/${locale}/tools/universal-format-converter`,
      siteName: APP_NAME,
      images: [
        {
          url: `${SITE_URL}/og-image.jpg`,
          width: 1200,
          height: 630,
          alt: toolName,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: `${toolName} | ${APP_NAME}`,
      description: toolDescription,
      images: [`${SITE_URL}/og-image.jpg`],
    },
  }
}

export default async function UniversalFormatConverterPage({
  params,
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params

  // Enable static rendering
  setRequestLocale(locale)

  // Get translations
  const t = await getTranslations({ locale })

  // Get markdown content if available
  const markdown = await getToolMarkdown(
    'universal-format-converter' as ToolSlugs,
    locale
  )

  return (
    <div className="container mx-auto">
      {/* Breadcrumbs */}
      <nav className="text-muted-foreground mb-6 flex items-center text-sm">
        <Link href={`/${locale}`} className="hover:text-foreground">
          {t('nav.home')}
        </Link>
        <ChevronRight className="mx-2 h-4 w-4" />
        <Link href={`/${locale}/tools`} className="hover:text-foreground">
          {t('nav.tools')}
        </Link>
        <ChevronRight className="mx-2 h-4 w-4" />
        <span className="text-foreground font-medium">
          {t('tools.universal-format-converter.title')}
        </span>
      </nav>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-3 lg:grid-cols-4">
        {/* Main Content */}
        <div className="md:col-span-2 lg:col-span-3">
          <div className="mb-8">
            <h1 className="mb-2 text-3xl font-bold tracking-tight">
              {t('tools.universal-format-converter.title')}
            </h1>
            <p className="text-muted-foreground">
              {t('tools.universal-format-converter.description')}
            </p>
          </div>

          <EnhancedToolSchema
            slug="universal-format-converter"
            rating={4.8}
            ratingCount={35}
            datePublished="2023-01-01"
            dateModified={new Date().toISOString().split('T')[0]}
          />

          {/* Conversion Tool */}
          <div className="mb-12">
            <NoSSRWrapper>
              <UniversalFormatConverter />
            </NoSSRWrapper>
          </div>

          {/* Markdown Content */}
          {markdown?.content && (
            <div className="mt-12 border-t pt-8">
              <h2 className="mb-6 text-2xl font-bold">About This Tool</h2>
              <MarkdownContent content={markdown.content} />
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="md:col-span-1">
          <RelatedToolsSidebar
            currentToolSlug="universal-format-converter"
            maxTools={8}
          />
        </div>
      </div>
    </div>
  )
}
