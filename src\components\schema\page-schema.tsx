import { APP_NAME, SITE_URL } from '@/lib/constants'

interface PageSchemaProps {
  slug: string
  title: string
  description: string
  locale: string
  datePublished?: string
  dateModified?: string
}

export function PageSchema({
  slug,
  title,
  description,
  locale,
  datePublished = '2024-01-01',
  dateModified = new Date().toISOString().split('T')[0],
}: PageSchemaProps) {
  // Create the JSON-LD schema for the page
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: title,
    description: description,
    url: `${SITE_URL}/${locale}/${slug}`,
    inLanguage: locale,
    datePublished: datePublished,
    dateModified: dateModified,
    publisher: {
      '@type': 'Organization',
      name: APP_NAME,
      url: SITE_URL,
    },
    breadcrumb: {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: `${SITE_URL}/${locale}`,
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: title,
          item: `${SITE_URL}/${locale}/${slug}`,
        },
      ],
    },
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  )
}
