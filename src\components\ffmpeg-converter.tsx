'use client'
import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile, toBlobURL } from '@ffmpeg/util'
import { useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { useTranslations } from 'next-intl'
import { Card, CardContent } from '@/components/ui/card'
import { ToolSlugs } from '@/lib/constants'

interface FFmpegConverterProps {
  inputFormat: string
  outputFormat: string
  ffmpegCommand: string[]
  outputMimeType: string
  toolSlug?: ToolSlugs
}

export const FFmpegConverter = ({
  inputFormat,
  outputFormat,
  ffmpegCommand,
  outputMimeType,
  toolSlug,
}: FFmpegConverterProps) => {
  const t = useTranslations()

  // Helper function to get the correct translation key
  const getTranslationKey = (key: string, defaultKey: string): string => {
    if (!toolSlug) return defaultKey

    // For specific tool slugs that cause issues, handle them directly
    if (toolSlug === ('universal-format-converter' as any)) {
      if (key === 'selectFile')
        return 'tools.universal-format-converter.selectFile'
      if (key === 'instructions')
        return 'tools.universal-format-converter.instructions'
    }

    // For all other cases, construct the key
    return `tools.${toolSlug}.${key}`
  }

  const [ffmpegLoaded, setFFmpegLoaded] = useState(false)
  const [isFFmpegLoading, setIsFFmpegLoading] = useState(false)
  const [isConverting, setIsConverting] = useState(false)
  const [progress, setProgress] = useState(0)
  const [file, setFile] = useState<File | null>(null)
  const [outputUrl, setOutputUrl] = useState<string | null>(null)
  // Create FFmpeg instance
  const ffmpegRef = useRef(new FFmpeg())
  const fileInputRef = useRef<HTMLInputElement | null>(null)
  const messageRef = useRef<HTMLParagraphElement | null>(null)

  const loadFFmpeg = async () => {
    setIsFFmpegLoading(true)
    const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.10/dist/umd'
    const ffmpeg = ffmpegRef.current

    try {
      // Set up logging
      ffmpeg.on('log', ({ message }) => {
        if (messageRef.current) messageRef.current.innerHTML = message
        // console.log(message)
      })

      // Set up progress tracking
      ffmpeg.on('progress', ({ progress }) => {
        setProgress(Math.min(100, Math.round(progress * 100)))
      })

      // toBlobURL is used to bypass CORS issue
      await ffmpeg.load({
        coreURL: await toBlobURL(
          `${baseURL}/ffmpeg-core.js`,
          'text/javascript'
        ),
        wasmURL: await toBlobURL(
          `${baseURL}/ffmpeg-core.wasm`,
          'application/wasm'
        ),
      })

      setFFmpegLoaded(true)
      setIsFFmpegLoading(false)
    } catch (error) {
      console.error('Error loading FFmpeg:', error)
      if (messageRef.current) {
        messageRef.current.innerHTML =
          'Failed to load FFmpeg. Please try again or use a smaller file.'
      }
      setIsFFmpegLoading(false)
    }
  }

  // Maximum file size in MB - increased to 500MB
  const MAX_FILE_SIZE_MB = 500

  const checkFileSize = (file: File): boolean => {
    const fileSizeMB = file.size / (1024 * 1024)
    if (fileSizeMB > MAX_FILE_SIZE_MB) {
      if (messageRef.current) {
        messageRef.current.innerHTML = `File is too large (${fileSizeMB.toFixed(2)} MB). Maximum allowed size is ${MAX_FILE_SIZE_MB} MB.`
        // Make the message visible
        messageRef.current.parentElement?.classList.remove('hidden')
        messageRef.current.parentElement?.classList.add(
          'bg-yellow-50',
          'p-4',
          'rounded-md',
          'border',
          'border-yellow-200',
          'mb-4'
        )
      }
      return false
    }
    return true
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      if (checkFileSize(selectedFile)) {
        setFile(selectedFile)
        setOutputUrl(null)
        // Hide any previous messages
        if (messageRef.current?.parentElement) {
          messageRef.current.parentElement.classList.add('hidden')
          messageRef.current.innerHTML = ''
        }
      }
    }
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    const droppedFile = event.dataTransfer.files[0]
    if (droppedFile) {
      if (checkFileSize(droppedFile)) {
        setFile(droppedFile)
        setOutputUrl(null)
        // Hide any previous messages
        if (messageRef.current?.parentElement) {
          messageRef.current.parentElement.classList.add('hidden')
          messageRef.current.innerHTML = ''
        }
      }
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
  }

  const convertFile = async () => {
    if (!file) return

    if (!ffmpegLoaded) await loadFFmpeg()

    try {
      setIsConverting(true)
      setProgress(0)
      setOutputUrl(null)

      const ffmpeg = ffmpegRef.current

      // Check file size and warn if it's too large
      const fileSizeMB = file.size / (1024 * 1024)
      if (fileSizeMB > 200) {
        if (messageRef.current) {
          messageRef.current.innerHTML =
            'Warning: Large file detected. Processing may take longer or fail due to memory constraints.'
          // Make the message visible
          messageRef.current.parentElement?.classList.remove('hidden')
          messageRef.current.parentElement?.classList.add(
            'bg-yellow-50',
            'p-4',
            'rounded-md',
            'border',
            'border-yellow-200',
            'mb-4'
          )
        }
      }

      // Determine input file extension from the file name
      const fileExt =
        file.name.split('.').pop()?.toLowerCase() || inputFormat.split(',')[0]
      const inputFileName = `input.${fileExt}`

      // Update the ffmpeg command to use the correct input file
      const updatedCommand = [...ffmpegCommand]
      const inputIndex = updatedCommand.indexOf('-i') + 1
      if (inputIndex > 0 && inputIndex < updatedCommand.length) {
        updatedCommand[inputIndex] = inputFileName
      }
      const outputFileName = `output.${outputFormat}`

      try {
        // Write the input file to FFmpeg's virtual file system
        await ffmpeg.writeFile(inputFileName, await fetchFile(file))

        // Add memory optimization flags to the command
        // Start with basic command (input file)
        const optimizedCommand = ['-i', inputFileName]

        // Add memory optimization flags
        optimizedCommand.push('-threads', '1') // Use single thread to reduce memory usage

        // Add format-specific options based on output format
        if (outputFormat === 'mp3') {
          // For MP3 output
          optimizedCommand.push(
            '-vn', // No video
            '-acodec',
            'libmp3lame',
            '-q:a',
            '5', // Lower quality (higher number = lower quality)
            '-map_metadata',
            '-1' // Remove metadata to save memory
          )
        } else if (outputFormat === 'mp4') {
          // For MP4 output
          optimizedCommand.push(
            '-c:v',
            'libx264',
            '-preset',
            'ultrafast',
            '-crf',
            '30', // Lower quality
            '-c:a',
            'aac',
            '-b:a',
            '96k', // Lower audio bitrate
            '-map_metadata',
            '-1' // Remove metadata
          )
        } else if (outputFormat === 'webm') {
          // For WebM output
          optimizedCommand.push(
            '-c:v',
            'libvpx',
            '-deadline',
            'realtime',
            '-cpu-used',
            '8', // Fastest CPU usage
            '-b:v',
            '1M', // Lower video bitrate
            '-c:a',
            'libvorbis',
            '-map_metadata',
            '-1' // Remove metadata
          )
        } else if (outputFormat === 'wav') {
          // For WAV output
          optimizedCommand.push(
            '-vn', // No video
            '-acodec',
            'pcm_s16le',
            '-map_metadata',
            '-1' // Remove metadata
          )
        } else if (outputFormat === 'gif') {
          // For GIF output - use palette generation for better quality and smaller size
          optimizedCommand.push(
            '-vf',
            'fps=10,scale=320:-1:flags=lanczos,split[s0][s1];[s0]palettegen=max_colors=64:stats_mode=diff[p];[s1][p]paletteuse=dither=bayer:bayer_scale=5:diff_mode=rectangle',
            '-loop',
            '0',
            '-map_metadata',
            '-1' // Remove metadata
          )
        }

        // Add output file
        optimizedCommand.push(outputFileName)

        // console.log('Running FFmpeg with command:', optimizedCommand)

        // Execute the optimized FFmpeg command
        await ffmpeg.exec(optimizedCommand)

        // Read the output file
        const data = await ffmpeg.readFile(outputFileName)

        // Create a download URL for the output file
        const url = URL.createObjectURL(
          new Blob([data instanceof Uint8Array ? data : data], {
            type: outputMimeType,
          })
        )

        setOutputUrl(url)

        // Show success message
        if (messageRef.current) {
          messageRef.current.innerHTML = t('tools.common.conversionSuccess')
          // Make the message visible with success styling
          messageRef.current.parentElement?.classList.remove('hidden')
          messageRef.current.parentElement?.classList.add(
            'bg-green-50',
            'p-4',
            'rounded-md',
            'border',
            'border-green-200',
            'mb-4'
          )
        }
      } catch (execError) {
        console.error('FFmpeg execution error:', execError)

        // Check if it's a memory error
        const errorMessage = String(execError)
        if (
          errorMessage.includes('memory') ||
          errorMessage.includes('allocation') ||
          errorMessage.includes('out of bound')
        ) {
          if (messageRef.current) {
            messageRef.current.innerHTML =
              "Memory error: The file is too large to process. We've optimized the conversion process to use less memory, but your file still exceeds the limits. Please try a smaller file, or try converting to a different format with lower quality settings."
            // Make the message visible with error styling
            messageRef.current.parentElement?.classList.remove('hidden')
            messageRef.current.parentElement?.classList.add(
              'bg-red-50',
              'p-4',
              'rounded-md',
              'border',
              'border-red-200',
              'mb-4'
            )
          }
        } else {
          if (messageRef.current) {
            messageRef.current.innerHTML = t('tools.common.conversionFailed')
            // Make the message visible with error styling
            messageRef.current.parentElement?.classList.remove('hidden')
            messageRef.current.parentElement?.classList.add(
              'bg-red-50',
              'p-4',
              'rounded-md',
              'border',
              'border-red-200',
              'mb-4'
            )
          }
        }
        throw execError // Re-throw to be caught by the outer catch
      }
    } catch (error) {
      console.error('Conversion error:', error)
      // The error message is already set in the inner catch block
    } finally {
      setIsConverting(false)
    }
  }

  return (
    <div className="space-y-8">
      {/* File Upload Area */}
      <Card>
        <CardContent>
          {!file ? (
            <div
              className="border-muted rounded-lg border-2 border-dashed p-8 text-center"
              onDrop={handleDrop}
              onDragOver={handleDragOver}
            >
              <div className="flex flex-col items-center justify-center space-y-4">
                <div className="bg-muted rounded-full p-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-muted-foreground"
                  >
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="17 8 12 3 7 8"></polyline>
                    <line x1="12" y1="3" x2="12" y2="15"></line>
                  </svg>
                </div>
                <div>
                  <p className="text-muted-foreground text-sm">
                    {t('tools.common.dropzone')}
                  </p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2"
                  >
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="17 8 12 3 7 8"></polyline>
                    <line x1="12" y1="3" x2="12" y2="15"></line>
                  </svg>
                  {t(
                    getTranslationKey(
                      'selectFile',
                      'tools.common.upload'
                    ) as any,
                    {}
                  )}
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept={inputFormat
                    .split(',')
                    .map((ext) => `.${ext}`)
                    .join(',')}
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* File List Item */}
              <div className="bg-muted/20 flex items-center rounded-md border p-3">
                <div className="flex-1 overflow-hidden">
                  <p className="truncate font-medium">{file.name}</p>
                  <p className="text-muted-foreground text-xs">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="ml-2"
                  onClick={() => {
                    setFile(null)
                    setOutputUrl(null)
                  }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M18 6L6 18M6 6l12 12" />
                  </svg>
                  <span className="sr-only">Remove</span>
                </Button>
              </div>

              {/* Convert Button */}
              <div className="flex justify-center">
                <Button
                  onClick={convertFile}
                  disabled={isConverting || isFFmpegLoading}
                  className="h-auto overflow-hidden [word-break:break-word] whitespace-normal"
                >
                  {isConverting || isFFmpegLoading ? (
                    <span className="mr-2 animate-spin">
                      <svg
                        viewBox="0 0 1024 1024"
                        focusable="false"
                        data-icon="loading"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path>
                      </svg>
                    </span>
                  ) : null}

                  {isConverting
                    ? t('tools.common.processing')
                    : isFFmpegLoading
                      ? t('tools.common.ffmpegLoading')
                      : t('tools.common.convert')}
                </Button>
              </div>
            </div>
          )}

          {/* Progress Bar */}
          {isConverting && (
            <div className="mt-4 space-y-2">
              <Progress value={progress} />
              <p className="text-muted-foreground text-center text-xs">
                {progress}%
              </p>
            </div>
          )}

          {/* Download Button */}
          {outputUrl && (
            <div className="mt-6 text-center">
              <Button asChild>
                <a
                  href={outputUrl}
                  download={`${
                    file?.name.split('.')[0] || 'file'
                  }_converted.${outputFormat}`}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2"
                  >
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7 10 12 15 17 10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                  </svg>
                  {t('tools.common.download')}
                </a>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardContent>
          <h3 className="mb-2 text-lg font-medium">
            {t('tools.common.instructions')}
          </h3>
          <p className="text-muted-foreground text-sm">
            {t(
              getTranslationKey(
                'instructions',
                'tools.common.uploadFirst'
              ) as any,
              {}
            )}
          </p>
        </CardContent>
      </Card>

      {/* Status Messages (hidden by default, shown when needed) */}
      <div className="hidden">
        <p
          ref={messageRef}
          className="text-muted-foreground min-h-[1.5rem] text-sm"
        ></p>
      </div>
    </div>
  )
}
