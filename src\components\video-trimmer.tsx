'use client'

import React, { useState, useRef, useCallback } from 'react'
import ReactPlayer from 'react-player'
import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile, toBlobURL } from '@ffmpeg/util'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { VideoTimeline } from '@/components/ui/video-timeline'
import {
  validateVideoFile,
  generateThumbnails,
  getVideoMetadata,
  formatTime
} from '@/lib/video-utils'
import { useTranslations } from 'next-intl'

interface VideoTrimmerProps {
  className?: string
}

export function VideoTrimmer({ className }: VideoTrimmerProps) {
  const t = useTranslations()

  // File and video state
  const [file, setFile] = useState<File | null>(null)
  const [videoUrl, setVideoUrl] = useState<string | null>(null)
  const [duration, setDuration] = useState<number>(0)
  const [thumbnails, setThumbnails] = useState<string[]>([])

  // Trimming state
  const [trimRange, setTrimRange] = useState<[number, number]>([0, 0])
  const [currentTime, setCurrentTime] = useState<number>(0)

  // Processing state
  const [isLoadingThumbnails, setIsLoadingThumbnails] = useState(false)
  const [isFFmpegLoading, setIsFFmpegLoading] = useState(false)
  const [ffmpegLoaded, setFFmpegLoaded] = useState(false)
  const [isTrimming, setIsTrimming] = useState(false)
  const [progress, setProgress] = useState(0)
  const [outputUrl, setOutputUrl] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Refs
  const playerRef = useRef<ReactPlayer>(null)
  const ffmpegRef = useRef<FFmpeg | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Initialize FFmpeg ref on client side only
  React.useEffect(() => {
    if (typeof window !== 'undefined' && !ffmpegRef.current) {
      ffmpegRef.current = new FFmpeg()
    }
  }, [])

  // Load FFmpeg
  const loadFFmpeg = useCallback(async () => {
    if (ffmpegLoaded || !ffmpegRef.current) return

    setIsFFmpegLoading(true)
    setError(null)

    try {
      const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.10/dist/umd'
      const ffmpeg = ffmpegRef.current

      ffmpeg.on('log', ({ message }) => {
        console.log(message)
      })

      ffmpeg.on('progress', ({ progress }) => {
        setProgress(Math.min(100, Math.round(progress * 100)))
      })

      await ffmpeg.load({
        coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
        wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
      })

      setFFmpegLoaded(true)
    } catch (error) {
      console.error('Error loading FFmpeg:', error)
      setError('Failed to load video processing engine. Please refresh and try again.')
    } finally {
      setIsFFmpegLoading(false)
    }
  }, [ffmpegLoaded])

  // Handle file selection
  const handleFileSelect = useCallback(async (selectedFile: File) => {
    setError(null)

    // Validate file
    const validation = validateVideoFile(selectedFile)
    if (!validation.isValid) {
      setError(validation.error || 'Invalid file')
      return
    }

    setFile(selectedFile)
    const url = URL.createObjectURL(selectedFile)
    setVideoUrl(url)

    try {
      // Get video metadata
      const metadata = await getVideoMetadata(selectedFile)
      setDuration(metadata.duration)
      setTrimRange([0, metadata.duration])

      // Generate thumbnails
      setIsLoadingThumbnails(true)
      const thumbs = await generateThumbnails(selectedFile, 10)
      setThumbnails(thumbs)
    } catch (error) {
      console.error('Error processing video:', error)
      setError('Error processing video file. Please try a different file.')
    } finally {
      setIsLoadingThumbnails(false)
    }
  }, [])

  // Handle file input change
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      handleFileSelect(selectedFile)
    }
  }

  // Handle drag and drop
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    const droppedFile = event.dataTransfer.files[0]
    if (droppedFile) {
      handleFileSelect(droppedFile)
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
  }

  // Handle video player events
  const handleProgress = (state: { playedSeconds: number }) => {
    setCurrentTime(state.playedSeconds)
  }

  const handleSeek = (time: number) => {
    if (playerRef.current) {
      playerRef.current.seekTo(time, 'seconds')
      setCurrentTime(time)
    }
  }

  // Handle range change
  const handleRangeChange = (newRange: [number, number]) => {
    setTrimRange(newRange)
  }

  // Preview selected clip
  const previewClip = () => {
    if (playerRef.current) {
      playerRef.current.seekTo(trimRange[0], 'seconds')
      setCurrentTime(trimRange[0])
    }
  }

  // Trim video
  const trimVideo = async () => {
    if (!file || !ffmpegRef.current) {
      setError('Video processing not available. Please refresh the page.')
      return
    }

    if (!ffmpegLoaded) {
      await loadFFmpeg()
      if (!ffmpegLoaded) return
    }

    setIsTrimming(true)
    setProgress(0)
    setError(null)
    setOutputUrl(null)

    try {
      const ffmpeg = ffmpegRef.current
      const inputFileName = `input.${file.name.split('.').pop()}`
      const outputFileName = `trimmed.mp4`

      // Write input file
      await ffmpeg.writeFile(inputFileName, await fetchFile(file))

      // Prepare FFmpeg command for trimming
      const startTime = trimRange[0]
      const duration = trimRange[1] - trimRange[0]

      const command = [
        '-i', inputFileName,
        '-ss', startTime.toString(),
        '-t', duration.toString(),
        '-c', 'copy', // Use stream copy for faster processing
        '-avoid_negative_ts', 'make_zero',
        outputFileName
      ]

      // Execute FFmpeg command
      await ffmpeg.exec(command)

      // Read output file
      const data = await ffmpeg.readFile(outputFileName)

      // Create download URL
      const url = URL.createObjectURL(
        new Blob([data instanceof Uint8Array ? data : data], {
          type: 'video/mp4',
        })
      )

      setOutputUrl(url)
    } catch (error) {
      console.error('Error trimming video:', error)
      setError('Failed to trim video. Please try again with a different file or range.')
    } finally {
      setIsTrimming(false)
    }
  }

  // Reset everything
  const resetAll = () => {
    setFile(null)
    setVideoUrl(null)
    setDuration(0)
    setThumbnails([])
    setTrimRange([0, 0])
    setCurrentTime(0)
    setOutputUrl(null)
    setError(null)
    setProgress(0)
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* File Upload */}
        {!file ? (
          <Card>
            <CardContent>
              <div
                className="border-muted rounded-lg border-2 border-dashed p-8 text-center"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                <div className="flex flex-col items-center justify-center space-y-4">
                  <div className="bg-muted rounded-full p-3">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-muted-foreground"
                    >
                      <path d="m15 2 3 3.3a1 1 0 0 1 .3.7v11.2a1 1 0 0 1-.3.7l-3 3.3"/>
                      <path d="M9 22H4a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1h5"/>
                      <path d="M7 8v4"/>
                      <path d="M5 10h4"/>
                    </svg>
                  </div>
                  <div>
                    <p className="text-muted-foreground text-sm">
                      Drag and drop a video file here, or click to select
                    </p>
                    <p className="text-muted-foreground text-xs mt-1">
                      Supports MP4, WebM, OGG, AVI, MOV (max 500MB)
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    Select Video File
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="video/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* Video Player */}
            <Card>
              <CardContent>
                <div className="aspect-video w-full overflow-hidden rounded-lg">
                  <ReactPlayer
                    ref={playerRef}
                    url={videoUrl}
                    width="100%"
                    height="100%"
                    controls
                    onProgress={handleProgress}
                    onDuration={setDuration}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Timeline and Controls */}
            {thumbnails.length > 0 && (
              <Card>
                <CardContent>
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Select Range to Trim</h3>

                    {isLoadingThumbnails ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                        <span className="ml-2 text-sm text-muted-foreground">
                          Generating thumbnails...
                        </span>
                      </div>
                    ) : (
                      <VideoTimeline
                        duration={duration}
                        thumbnails={thumbnails}
                        value={trimRange}
                        onChange={handleRangeChange}
                        currentTime={currentTime}
                        onSeek={handleSeek}
                      />
                    )}

                    <div className="flex items-center justify-between">
                      <div className="text-sm text-muted-foreground">
                        Selected: {formatTime(trimRange[0])} - {formatTime(trimRange[1])}
                        ({formatTime(trimRange[1] - trimRange[0])})
                      </div>
                      <Button variant="outline" onClick={previewClip}>
                        Preview Selection
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Trim Controls */}
            {file && thumbnails.length > 0 && (
              <Card>
                <CardContent>
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Trim Video</h3>

                    {/* Progress Bar */}
                    {(isTrimming || isFFmpegLoading) && (
                      <div className="space-y-2">
                        <Progress value={progress} />
                        <p className="text-muted-foreground text-center text-sm">
                          {isFFmpegLoading ? 'Loading video processor...' : `Trimming video... ${progress}%`}
                        </p>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex flex-wrap gap-3">
                      <Button
                        onClick={trimVideo}
                        disabled={isTrimming || isFFmpegLoading || trimRange[1] - trimRange[0] <= 0}
                        className="flex-1 min-w-[120px]"
                      >
                        {isTrimming || isFFmpegLoading ? (
                          <span className="mr-2 animate-spin">
                            <svg
                              viewBox="0 0 1024 1024"
                              focusable="false"
                              data-icon="loading"
                              width="1em"
                              height="1em"
                              fill="currentColor"
                              aria-hidden="true"
                            >
                              <path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path>
                            </svg>
                          </span>
                        ) : (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="mr-2"
                          >
                            <path d="M9 12l2 2 4-4"/>
                            <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"/>
                          </svg>
                        )}
                        {isTrimming ? 'Trimming...' : isFFmpegLoading ? 'Loading...' : 'Trim Video'}
                      </Button>

                      <Button variant="outline" onClick={resetAll}>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-2"
                        >
                          <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
                          <path d="M3 3v5h5"/>
                        </svg>
                        Start Over
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Download Section */}
            {outputUrl && (
              <Card>
                <CardContent>
                  <div className="space-y-4 text-center">
                    <div className="bg-green-50 border border-green-200 rounded-md p-4">
                      <h3 className="text-lg font-medium text-green-800 mb-2">
                        Video Trimmed Successfully!
                      </h3>
                      <p className="text-green-700 text-sm">
                        Your trimmed video is ready for download.
                      </p>
                    </div>

                    <Button asChild size="lg">
                      <a
                        href={outputUrl}
                        download={`${file?.name.split('.')[0] || 'video'}_trimmed.mp4`}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-2"
                        >
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                          <polyline points="7 10 12 15 17 10"/>
                          <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                        Download Trimmed Video
                      </a>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </>
        )}

        {/* Error Display */}
        {error && (
          <Card>
            <CardContent>
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-red-800 text-sm">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
