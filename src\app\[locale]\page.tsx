import { getTranslations } from 'next-intl/server'
import { <PERSON> } from '@/i18n/navigation'
import { ArrowRight } from 'lucide-react'
import { routing } from '@/i18n/routing'

import { Button } from '@/components/ui/button'
import { HomeHero } from '@/components/home-hero'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { TOOLS, TOOL_CATEGORIES, APP_NAME, SITE_URL } from '@/lib/constants'
import { HomeSchema } from '@/components/schema/home-schema'

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }))
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params
  const t = await getTranslations({ locale, namespace: 'app' })

  // Create a title with tagline for better SEO
  const titleWithTagline = `${t('name')} - ${t('tagline')}`

  return {
    title: titleWithTagline,
    description: t('description'),
    alternates: {
      canonical: `/${locale}`,
      languages: Object.fromEntries(routing.locales.map((l) => [l, `/${l}`])),
    },
    openGraph: {
      type: 'website',
      title: titleWithTagline,
      description: t('description'),
      url: `${SITE_URL}/${locale}`,
      siteName: APP_NAME,
      images: [
        {
          url: `${SITE_URL}/og-image.jpg`,
          width: 1200,
          height: 630,
          alt: APP_NAME,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: titleWithTagline,
      description: t('description'),
      images: [`${SITE_URL}/og-image.jpg`],
    },
  }
}

export default async function Home({
  params,
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params
  const t = await getTranslations({ locale })

  // Group tools by category
  const toolsByCategory = TOOL_CATEGORIES.map((category) => {
    return {
      ...category,
      tools: TOOLS.filter((tool) => tool.category === category.slug),
    }
  })

  return (
    <div className="space-y-12">
      {/* Add Schema.org structured data */}
      <HomeSchema />

      {/* Hero Section - Client Component */}
      <HomeHero />

      {/* Tools Section */}
      <section className="space-y-8">
        {toolsByCategory.map((category) => (
          <div key={category.slug} className="space-y-4">
            <h2 className="text-2xl font-bold tracking-tight">
              {t(`tools.categories.${category.slug}`)}
            </h2>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {category.tools.map((tool) => (
                <Link
                  href={`/tools/${tool.slug}`}
                  key={tool.slug}
                  className="block transition-transform hover:scale-[1.02]"
                >
                  <Card className="hover:border-primary flex h-full cursor-pointer flex-col border-2">
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        {t(`tools.${tool.slug}.title`)}
                        <ArrowRight className="text-muted-foreground h-5 w-5" />
                      </CardTitle>
                      <CardDescription>
                        {t(`tools.${tool.slug}.description`)}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-1">
                      <p className="text-sm">
                        {t(`tools.${tool.slug}.instructions`)}
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button className="w-full">
                        {t('tools.common.convert')}
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        ))}
      </section>
    </div>
  )
}
