'use client'

import React, { useState, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

interface CropArea {
  x: number
  y: number
  width: number
  height: number
}

interface VideoCropperProps {
  videoSrc: string
  onCropChange: (cropArea: CropArea) => void
  className?: string
}

export const VideoCropper: React.FC<VideoCropperProps> = ({
  videoSrc,
  onCropChange,
  className = '',
}) => {
  const [cropArea, setCropArea] = useState<CropArea>({ x: 0, y: 0, width: 640, height: 480 })

  // Handle crop area changes
  const handleCropChange = useCallback((field: keyof CropArea, value: number) => {
    const newCropArea = { ...cropArea, [field]: value }
    setCropArea(newCropArea)
    onCropChange(newCropArea)
  }, [cropArea, onCropChange])

  // Preset aspect ratios
  const aspectRatioPresets = [
    { label: 'Free', ratio: null },
    { label: '16:9', ratio: 16/9 },
    { label: '4:3', ratio: 4/3 },
    { label: '1:1', ratio: 1 },
    { label: '9:16', ratio: 9/16 },
  ]

  const applyAspectRatio = useCallback((ratio: number | null) => {
    if (!ratio) return
    
    const newHeight = Math.round(cropArea.width / ratio)
    const newCropArea = { ...cropArea, height: newHeight }
    setCropArea(newCropArea)
    onCropChange(newCropArea)
  }, [cropArea, onCropChange])

  if (!videoSrc) {
    return (
      <div className={`bg-gray-100 rounded-lg p-8 text-center text-gray-500 ${className}`}>
        Upload a video to start cropping
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Video Preview */}
      <Card>
        <CardContent className="p-4">
          <div className="relative bg-black rounded-lg overflow-hidden">
            <video
              src={videoSrc}
              className="w-full h-auto max-h-96 object-contain"
              controls
              muted
            />
            {/* Crop overlay visualization */}
            <div 
              className="absolute border-2 border-red-500 bg-red-500/20"
              style={{
                left: `${(cropArea.x / 1920) * 100}%`,
                top: `${(cropArea.y / 1080) * 100}%`,
                width: `${(cropArea.width / 1920) * 100}%`,
                height: `${(cropArea.height / 1080) * 100}%`,
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Crop Controls */}
      <div className="space-y-4">
        {/* Aspect Ratio Presets */}
        <div>
          <label className="block text-sm font-medium mb-2">Aspect Ratio</label>
          <div className="flex flex-wrap gap-2">
            {aspectRatioPresets.map((preset) => (
              <Button
                key={preset.label}
                variant="outline"
                size="sm"
                onClick={() => applyAspectRatio(preset.ratio)}
              >
                {preset.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Manual Crop Controls */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">X Position</label>
            <input
              type="number"
              value={cropArea.x}
              onChange={(e) => handleCropChange('x', parseInt(e.target.value) || 0)}
              className="w-full px-3 py-2 border rounded-md"
              min="0"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Y Position</label>
            <input
              type="number"
              value={cropArea.y}
              onChange={(e) => handleCropChange('y', parseInt(e.target.value) || 0)}
              className="w-full px-3 py-2 border rounded-md"
              min="0"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Width</label>
            <input
              type="number"
              value={cropArea.width}
              onChange={(e) => handleCropChange('width', parseInt(e.target.value) || 1)}
              className="w-full px-3 py-2 border rounded-md"
              min="1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Height</label>
            <input
              type="number"
              value={cropArea.height}
              onChange={(e) => handleCropChange('height', parseInt(e.target.value) || 1)}
              className="w-full px-3 py-2 border rounded-md"
              min="1"
            />
          </div>
        </div>

        {/* Crop Information */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium mb-2">Crop Information</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Position:</span>
              <div>X: {cropArea.x}px</div>
              <div>Y: {cropArea.y}px</div>
            </div>
            <div>
              <span className="text-gray-600">Size:</span>
              <div>Width: {cropArea.width}px</div>
              <div>Height: {cropArea.height}px</div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <Card>
          <CardContent className="p-4">
            <h4 className="font-medium mb-2">How to Use</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Adjust the X and Y position to move the crop area</li>
              <li>• Set the width and height for the crop size</li>
              <li>• Use aspect ratio presets for common formats</li>
              <li>• The red overlay shows the crop area preview</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
