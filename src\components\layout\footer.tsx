'use client'

import { useTranslations } from 'next-intl'
import { Link, usePathname, useRouter } from '@/i18n/navigation'
import { useLocale } from 'next-intl'
import { APP_NAME, LANGUAGES } from '@/lib/constants'

export default function Footer() {
  const t = useTranslations('footer')
  const currentYear = new Date().getFullYear().toString()
  const currentLocale = useLocale()
  const pathname = usePathname()
  const router = useRouter()

  return (
    <footer className="border-t py-6">
      <div className="container mx-auto px-4">
        {/* Language List */}
        <div className="mb-4">
          <div className="flex flex-wrap justify-center gap-x-4 gap-y-2">
            {LANGUAGES.map((language) => (
              <button
                key={language.code}
                onClick={() => {
                  // Use router.push with the replace option to avoid adding to history
                  router.push(pathname, {
                    locale: language.code,
                    scroll: false,
                  })
                }}
                className={`cursor-pointer text-xs ${
                  currentLocale === language.code
                    ? 'text-foreground font-semibold'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
                aria-current={
                  currentLocale === language.code ? 'page' : undefined
                }
                lang={language.code}
              >
                {language.name}
              </button>
            ))}
          </div>

          {/* Hidden links for SEO crawlers */}
          <div className="sr-only">
            {LANGUAGES.map((language) => (
              <Link
                key={`seo-${language.code}`}
                href={pathname}
                locale={language.code}
                hrefLang={language.code}
              >
                {language.name}
              </Link>
            ))}
          </div>
        </div>

        <div className="border-t pt-4">
          <div className="flex flex-col items-center justify-between md:flex-row">
            <div className="mb-4 md:mb-0">
              <p className="text-muted-foreground text-sm">
                {t('copyright', { year: currentYear, appName: APP_NAME })}
              </p>
            </div>
            <div className="flex flex-wrap justify-center gap-x-6 gap-y-2 md:justify-end">
              <Link
                href="/about"
                className="text-muted-foreground hover:text-foreground text-sm"
              >
                {t('about')}
              </Link>
              <Link
                href="/contact"
                className="text-muted-foreground hover:text-foreground text-sm"
              >
                {t('contact')}
              </Link>
              <Link
                href="/privacy"
                className="text-muted-foreground hover:text-foreground text-sm"
              >
                {t('privacy')}
              </Link>
              <Link
                href="/terms"
                className="text-muted-foreground hover:text-foreground text-sm"
              >
                {t('terms')}
              </Link>
              <Link
                href="/dmca"
                className="text-muted-foreground hover:text-foreground text-sm"
              >
                {t('dmca')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
