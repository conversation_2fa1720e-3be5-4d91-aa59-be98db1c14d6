'use client'

import * as React from 'react'
import { cn } from '@/lib/utils'

interface RangeSliderProps {
  min?: number
  max?: number
  step?: number
  value?: [number, number]
  onChange?: (value: [number, number]) => void
  className?: string
  disabled?: boolean
}

export function RangeSlider({
  min = 0,
  max = 100,
  step = 1,
  value = [min, max],
  onChange,
  className,
  disabled = false,
}: RangeSliderProps) {
  const [internalValue, setInternalValue] = React.useState<[number, number]>(value)
  const [isDragging, setIsDragging] = React.useState<'start' | 'end' | null>(null)
  const trackRef = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    setInternalValue(value)
  }, [value])

  const handleMouseDown = (handle: 'start' | 'end') => (e: React.MouseEvent) => {
    if (disabled) return
    e.preventDefault()
    setIsDragging(handle)
  }

  const handleMouseMove = React.useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !trackRef.current || disabled) return

      const rect = trackRef.current.getBoundingClientRect()
      const percentage = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width))
      const newValue = min + percentage * (max - min)
      const steppedValue = Math.round(newValue / step) * step

      setInternalValue(prev => {
        let newRange: [number, number]
        
        if (isDragging === 'start') {
          newRange = [Math.min(steppedValue, prev[1]), prev[1]]
        } else {
          newRange = [prev[0], Math.max(steppedValue, prev[0])]
        }
        
        onChange?.(newRange)
        return newRange
      })
    },
    [isDragging, min, max, step, onChange, disabled]
  )

  const handleMouseUp = React.useCallback(() => {
    setIsDragging(null)
  }, [])

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  const startPercentage = ((internalValue[0] - min) / (max - min)) * 100
  const endPercentage = ((internalValue[1] - min) / (max - min)) * 100

  return (
    <div className={cn('relative h-6 w-full', className)}>
      {/* Track */}
      <div
        ref={trackRef}
        className={cn(
          'bg-muted absolute top-1/2 h-2 w-full -translate-y-1/2 rounded-full',
          disabled && 'opacity-50'
        )}
      />
      
      {/* Active range */}
      <div
        className={cn(
          'bg-primary absolute top-1/2 h-2 -translate-y-1/2 rounded-full',
          disabled && 'opacity-50'
        )}
        style={{
          left: `${startPercentage}%`,
          width: `${endPercentage - startPercentage}%`,
        }}
      />
      
      {/* Start handle */}
      <div
        className={cn(
          'bg-background border-primary absolute top-1/2 h-4 w-4 -translate-x-1/2 -translate-y-1/2 cursor-pointer rounded-full border-2 shadow-sm transition-all',
          isDragging === 'start' && 'scale-110',
          disabled && 'cursor-not-allowed opacity-50'
        )}
        style={{ left: `${startPercentage}%` }}
        onMouseDown={handleMouseDown('start')}
      />
      
      {/* End handle */}
      <div
        className={cn(
          'bg-background border-primary absolute top-1/2 h-4 w-4 -translate-x-1/2 -translate-y-1/2 cursor-pointer rounded-full border-2 shadow-sm transition-all',
          isDragging === 'end' && 'scale-110',
          disabled && 'cursor-not-allowed opacity-50'
        )}
        style={{ left: `${endPercentage}%` }}
        onMouseDown={handleMouseDown('end')}
      />
    </div>
  )
}
