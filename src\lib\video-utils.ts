/**
 * Utility functions for video processing and manipulation
 */

/**
 * Format time in seconds to MM:SS format
 */
export function formatTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

/**
 * Parse time string (MM:SS) to seconds
 */
export function parseTime(timeString: string): number {
  const [minutes, seconds] = timeString.split(':').map(Number)
  return minutes * 60 + seconds
}

/**
 * Generate thumbnails from video file at specified intervals
 */
export async function generateThumbnails(
  videoFile: File,
  count: number = 10
): Promise<string[]> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      reject(new Error('Could not get canvas context'))
      return
    }

    const thumbnails: string[] = []
    let currentIndex = 0

    video.addEventListener('loadedmetadata', () => {
      const duration = video.duration
      const interval = duration / count
      
      // Set canvas size
      canvas.width = 160
      canvas.height = 90

      const generateThumbnail = () => {
        if (currentIndex >= count) {
          resolve(thumbnails)
          return
        }

        const time = currentIndex * interval
        video.currentTime = time
      }

      const onSeeked = () => {
        // Draw video frame to canvas
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
        
        // Convert canvas to data URL
        const thumbnail = canvas.toDataURL('image/jpeg', 0.7)
        thumbnails.push(thumbnail)
        
        currentIndex++
        generateThumbnail()
      }

      video.addEventListener('seeked', onSeeked)
      generateThumbnail()
    })

    video.addEventListener('error', () => {
      reject(new Error('Error loading video'))
    })

    video.src = URL.createObjectURL(videoFile)
    video.load()
  })
}

/**
 * Get video metadata (duration, dimensions, etc.)
 */
export async function getVideoMetadata(videoFile: File): Promise<{
  duration: number
  width: number
  height: number
  size: number
}> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    
    video.addEventListener('loadedmetadata', () => {
      resolve({
        duration: video.duration,
        width: video.videoWidth,
        height: video.videoHeight,
        size: videoFile.size
      })
    })

    video.addEventListener('error', () => {
      reject(new Error('Error loading video metadata'))
    })

    video.src = URL.createObjectURL(videoFile)
    video.load()
  })
}

/**
 * Validate video file
 */
export function validateVideoFile(file: File): { isValid: boolean; error?: string } {
  const maxSize = 500 * 1024 * 1024 // 500MB
  const allowedTypes = ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov', 'video/quicktime']
  
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File size too large. Maximum allowed size is ${maxSize / 1024 / 1024}MB`
    }
  }

  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Invalid file type. Please upload a video file (MP4, WebM, OGG, AVI, MOV)'
    }
  }

  return { isValid: true }
}
