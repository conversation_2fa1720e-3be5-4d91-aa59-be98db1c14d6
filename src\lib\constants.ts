export const APP_NAME = 'VDO Tools'
export const DEFAULT_LANGUAGE = 'en'
export const SITE_URL = 'https://vdo.tools'
export const CONTACT_EMAIL = '<EMAIL>'
export const COMPANY_NAME = 'VDO Tools'
export const COPYRIGHT_YEAR = new Date().getFullYear()
export const SOCIAL_LINKS = {
  twitter: 'https://twitter.com/vdotools',
  facebook: 'https://facebook.com/vdotools',
  instagram: 'https://instagram.com/vdotools',
}

export type ToolCategory = {
  name: string
  slug: string
  description: string
}

export type Tool = {
  name: string
  slug: string
  description: string
  category: string
  icon: string
}

export const TOOL_CATEGORIES = [
  {
    name: 'Video to Audio',
    slug: 'video-to-audio',
    description: 'Convert video files to audio formats',
  },
  {
    name: 'Video Format Converters',
    slug: 'video-format-converters',
    description: 'Convert videos between different formats',
  },
  {
    name: 'Audio Format Converters',
    slug: 'audio-format-converters',
    description: 'Convert audio between different formats',
  },
  {
    name: 'Recording Tools',
    slug: 'recording-tools',
    description: 'Record your screen and save as video',
  },
] as const

export const TOOLS = [
  {
    name: 'MP4 to MP3',
    slug: 'mp4-to-mp3',
    description: 'Convert MP4 videos to MP3 audio files',
    category: 'video-to-audio',
    icon: 'music',
  },
  {
    name: 'Video to Audio',
    slug: 'video-to-audio',
    description: 'Extract audio from any video format',
    category: 'video-to-audio',
    icon: 'headphones',
  },
  {
    name: 'Universal Format Converter',
    slug: 'universal-format-converter',
    description: 'Auto-detect input format and convert to any format',
    category: 'video-format-converters',
    icon: 'wand-2',
  },
  {
    name: 'MP4 to WebM',
    slug: 'mp4-to-webm',
    description: 'Convert MP4 videos to WebM format',
    category: 'video-format-converters',
    icon: 'video',
  },
  {
    name: 'WebM to MP4',
    slug: 'webm-to-mp4',
    description: 'Convert WebM videos to MP4 format',
    category: 'video-format-converters',
    icon: 'video',
  },
  {
    name: 'MOV to MP4',
    slug: 'mov-to-mp4',
    description: 'Convert MOV videos to MP4 format',
    category: 'video-format-converters',
    icon: 'video',
  },
  {
    name: 'AVI to MP4',
    slug: 'avi-to-mp4',
    description: 'Convert AVI videos to MP4 format',
    category: 'video-format-converters',
    icon: 'video',
  },
  {
    name: 'MP4 to GIF',
    slug: 'mp4-to-gif',
    description: 'Convert MP4 videos to animated GIF images',
    category: 'video-format-converters',
    icon: 'image',
  },
  {
    name: 'GIF to MP4',
    slug: 'gif-to-mp4',
    description: 'Convert animated GIF images to MP4 videos',
    category: 'video-format-converters',
    icon: 'video',
  },
  {
    name: 'MP3 to WAV',
    slug: 'mp3-to-wav',
    description: 'Convert MP3 audio to WAV format',
    category: 'audio-format-converters',
    icon: 'file-audio',
  },
  {
    name: 'WAV to MP3',
    slug: 'wav-to-mp3',
    description: 'Convert WAV audio to MP3 format',
    category: 'audio-format-converters',
    icon: 'file-audio',
  },
  {
    name: 'Screen Recorder',
    slug: 'screen-recorder',
    description: 'Record your screen and save as video',
    category: 'recording-tools',
    icon: 'video-camera',
  },
] as const

export type ToolSlugs = (typeof TOOLS)[number]['slug']

export const RATING_DEFAULT = 0
export const MAX_RATING = 5

export const LOCALES = ['en', 'es', 'fr', 'pt', 'zh', 'de', 'ja', 'ru', 'it']
export const DEFAULT_LOCALE = 'en'
export const LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'es', name: 'Español' },
  { code: 'fr', name: 'Français' },
  { code: 'pt', name: 'Português' },
  { code: 'zh', name: '中文' },
  { code: 'de', name: 'Deutsch' },
  { code: 'ja', name: '日本語' },
  { code: 'ru', name: 'Русский' },
  { code: 'it', name: 'Italiano' },
]

// Define navigation data
export const mainNavItems = [
  { id: 'home', name: 'Home', href: '/', translationKey: 'nav.home' },
  { id: 'tools', name: 'Tools', href: '/tools', translationKey: 'nav.tools' },
] as const

// Define categories and their items
export const navCategories = {
  Tools: [
    {
      name: 'Video to Audio Tools',
      href: '/tools#video-to-audio',
      translationKey: 'categories.videoToAudioTools',
      items: [
        {
          name: 'MP4 to MP3',
          href: '/tools/mp4-to-mp3',
          translationKey: 'tools_list.mp4ToMp3',
        },
        {
          name: 'Video to Audio',
          href: '/tools/video-to-audio',
          translationKey: 'tools_list.videoToAudio',
        },
      ],
    },
    {
      name: 'Video Format Converters',
      href: '/tools#video-format-converters',
      translationKey: 'tools.categories.video-format-converters',
      items: [
        {
          name: 'Universal Format Converter',
          href: '/tools/universal-format-converter',
          translationKey: 'tools_list.universalFormatConverter',
        },
        {
          name: 'MP4 to WebM',
          href: '/tools/mp4-to-webm',
          translationKey: 'tools_list.mp4ToWebm',
        },
        {
          name: 'WebM to MP4',
          href: '/tools/webm-to-mp4',
          translationKey: 'tools_list.webmToMp4',
        },
        {
          name: 'MOV to MP4',
          href: '/tools/mov-to-mp4',
          translationKey: 'tools_list.movToMp4',
        },
        {
          name: 'AVI to MP4',
          href: '/tools/avi-to-mp4',
          translationKey: 'tools_list.aviToMp4',
        },
        {
          name: 'MP4 to GIF',
          href: '/tools/mp4-to-gif',
          translationKey: 'tools_list.mp4ToGif',
        },
        {
          name: 'GIF to MP4',
          href: '/tools/gif-to-mp4',
          translationKey: 'tools_list.gifToMp4',
        },
      ],
    },
    {
      name: 'Audio Format Converters',
      href: '/tools#audio-format-converters',
      translationKey: 'tools.categories.audio-format-converters',
      items: [
        {
          name: 'MP3 to WAV',
          href: '/tools/mp3-to-wav',
          translationKey: 'tools_list.mp3ToWav',
        },
        {
          name: 'WAV to MP3',
          href: '/tools/wav-to-mp3',
          translationKey: 'tools_list.wavToMp3',
        },
        {
          name: 'Audio Converter',
          href: '/tools/audio-converter',
          translationKey: 'tools_list.audioConverter',
        },
        {
          name: 'Audio Extractor',
          href: '/tools/audio-extractor',
          translationKey: 'tools_list.audioExtractor',
        },
      ],
    },
    {
      name: 'Recording Tools',
      href: '/tools#recording-tools',
      translationKey: 'tools.categories.recording-tools',
      items: [
        {
          name: 'Screen Recorder',
          href: '/tools/screen-recorder',
          translationKey: 'tools_list.screenRecorder',
        },
        {
          name: 'Video Editor',
          href: '/tools/video-editor',
          translationKey: 'tools_list.videoEditor',
        },
        {
          name: 'Video Compressor',
          href: '/tools/video-compressor',
          translationKey: 'tools_list.videoCompressor',
        },
      ],
    },
  ],
} as const
