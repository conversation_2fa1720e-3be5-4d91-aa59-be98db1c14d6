'use client'

import { useTranslations } from 'next-intl'
import { Link } from '@/i18n/navigation'
import { TOOLS, ToolSlugs } from '@/lib/constants'
import { ToolsSearch } from '@/components/tools-search'

interface RelatedToolsSidebarProps {
  currentToolSlug: ToolSlugs
  maxTools?: number
}

export function RelatedToolsSidebar({
  currentToolSlug,
  maxTools = 5,
}: RelatedToolsSidebarProps) {
  const t = useTranslations()

  // Find the current tool to get its category
  const currentTool = TOOLS.find((tool) => tool.slug === currentToolSlug)

  if (!currentTool) {
    return null
  }

  // Find related tools (same category, excluding current tool)
  const relatedTools = TOOLS.filter(
    (tool) =>
      tool.category === currentTool.category && tool.slug !== currentToolSlug
  ).slice(0, maxTools)

  // If there are not enough tools in the same category, add some from other categories
  if (relatedTools.length < maxTools) {
    const otherTools = TOOLS.filter(
      (tool) =>
        tool.category !== currentTool.category && tool.slug !== currentToolSlug
    ).slice(0, maxTools - relatedTools.length)

    relatedTools.push(...otherTools)
  }

  if (relatedTools.length === 0) {
    return null
  }

  return (
    <div className="bg-card sticky top-8 rounded-lg border p-4 shadow-sm">
      {/* Search Box */}
      <ToolsSearch currentToolSlug={currentToolSlug} />

      {/* Related Tools */}
      <h3 className="mb-2 text-lg font-medium">{t('sidebar.relatedTools')}</h3>
      <div className="flex flex-col space-y-1">
        {relatedTools.map((tool) => (
          <Link
            key={tool.slug}
            href={`/tools/${tool.slug}`}
            className="hover:text-primary border-border/40 border-b py-1.5 text-sm transition-colors last:border-0"
          >
            {t(`tools.${tool.slug}.title`)}
          </Link>
        ))}
      </div>
    </div>
  )
}
