'use client'

import React from 'react'
import { useTranslations } from 'next-intl'
import { Link } from '@/i18n/navigation'
import { ChevronDown, ChevronRight } from 'lucide-react'
import { LanguageSwitcher } from '@/components/switchers/language-switcher'
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu'
import { SheetClose } from '@/components/ui/sheet'
import { mainNavItems, navCategories } from '@/lib/constants'

// Define types for our navigation items
export type SubItem = {
  name: string
  href: string
  description?: string
  translationKey?: string
}

export type NavCategory = {
  name: string
  href: string
  description?: string
  translationKey?: string
  items: SubItem[]
}

// Props for the navigation menu items
interface NavigationMenuItemsProps {
  mainItems: typeof mainNavItems
  categories: typeof navCategories
}

// Desktop navigation items
export function DesktopNavigationItems({
  mainItems,
  categories,
}: NavigationMenuItemsProps) {
  const t = useTranslations()

  return (
    <NavigationMenu className="relative">
      <NavigationMenuList>
        {/* Main navigation items */}
        {mainItems?.map((item) => (
          <NavigationMenuItem key={item.href}>
            <NavigationMenuLink
              asChild
              className={navigationMenuTriggerStyle()}
            >
              <Link href={item.href}>{t(`nav.${item.id}`) || item.name}</Link>
            </NavigationMenuLink>
          </NavigationMenuItem>
        ))}

        {/* Tools Mega Menu */}
        <NavigationMenuItem>
          <NavigationMenuTrigger>{t('nav.tools')}</NavigationMenuTrigger>
          <NavigationMenuContent>
            <div className="mx-auto w-screen max-w-6xl px-4">
              <div className="bg-popover rounded-md border shadow-lg">
                <div className="grid grid-cols-1 gap-6 p-6 md:grid-cols-2 lg:grid-cols-4">
                  {categories.Tools.map((category) => (
                    <div key={category.href} className="space-y-3">
                      <Link
                        href={category.href}
                        className="text-foreground hover:text-primary mb-2 block border-b pb-2 text-lg font-medium whitespace-nowrap"
                      >
                        {t(category.translationKey)}
                      </Link>

                      <ul className="space-y-1">
                        {category.items.map((item) => (
                          <li key={item.href}>
                            <NavigationMenuLink asChild>
                              <Link
                                href={item.href}
                                className="hover:text-primary block py-1 text-sm hover:underline"
                              >
                                {t(item.translationKey)}
                              </Link>
                            </NavigationMenuLink>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
                <div className="bg-muted/50 flex justify-center rounded-b-md p-4">
                  <Link
                    href="/tools"
                    className="text-primary hover:text-primary/80 text-sm font-medium"
                  >
                    {t('sidebar.allTools')}
                  </Link>
                </div>
              </div>
            </div>
          </NavigationMenuContent>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  )
}

// Props for mobile navigation items
interface MobileNavigationItemsProps {
  onClose: () => void
  mainItems: typeof mainNavItems
  categories: typeof navCategories
}

// Mobile navigation items
export function MobileNavigationItems({
  onClose,
  mainItems,
  categories,
}: MobileNavigationItemsProps) {
  const t = useTranslations()
  const [expandedToolCategories, setExpandedToolCategories] = React.useState<
    Record<string, boolean>
  >({})

  const toggleSubCategory = (categoryHref: string) => {
    setExpandedToolCategories((prev) => ({
      ...prev,
      [categoryHref]: !prev[categoryHref],
    }))
  }

  return (
    <div className="flex h-full flex-col overflow-y-auto">
      {/* Main Menu Items */}
      <div className="mb-2 space-y-1">
        {mainItems.map((item) => (
          <SheetClose key={item.href} asChild>
            <Link
              href={item.href}
              className="hover:bg-accent block w-full rounded-md px-4 py-2 font-medium"
              onClick={onClose}
            >
              {t(item.translationKey)}
            </Link>
          </SheetClose>
        ))}
      </div>

      {/* Tools Categories - Direct Access */}
      <div className="border-border/50 space-y-1 border-t pt-2">
        {categories.Tools.map((category) => (
          <div key={category.href}>
            <button
              className="hover:bg-accent flex w-full items-center justify-between rounded-md px-4 py-2 text-left font-medium"
              onClick={() => toggleSubCategory(category.href)}
            >
              <span>{t(category.translationKey)}</span>
              {expandedToolCategories[category.href] ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </button>

            {expandedToolCategories[category.href] && (
              <div className="border-border/50 ml-3 space-y-0.5 border-l py-1 pl-3">
                {category.items.map((item) => (
                  <SheetClose key={item.href} asChild>
                    <Link
                      href={item.href}
                      className="text-muted-foreground hover:text-foreground hover:bg-accent block rounded-md px-4 py-1.5 text-sm"
                      onClick={onClose}
                    >
                      {t(item.translationKey)}
                    </Link>
                  </SheetClose>
                ))}
              </div>
            )}
          </div>
        ))}

        <SheetClose asChild>
          <Link
            href="/tools"
            className="hover:bg-accent text-primary block w-full rounded-md px-4 py-2 text-sm font-medium"
            onClick={onClose}
          >
            {t('sidebar.allTools')}
          </Link>
        </SheetClose>
      </div>

      {/* Language Switcher at the end */}
      <div className="border-border/50 mt-auto border-t pt-3 pb-1">
        <div className="px-4">
          <LanguageSwitcher />
        </div>
      </div>
    </div>
  )
}
