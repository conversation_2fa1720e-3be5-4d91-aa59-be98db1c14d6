'use client'
import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile, toBlobURL } from '@ffmpeg/util'
import { useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  VIDEO_FORMATS,
  getFFmpegCommand,
  getMimeType,
  detectFormatFromFileName,
} from '@/lib/video-formats'
import { isAudioFormat } from '@/lib/format-utils'

export const UniversalFormatConverter = () => {
  const t = useTranslations()
  const [ffmpegLoaded, setFFmpegLoaded] = useState(false)
  const [isFFmpegLoading, setIsFFmpegLoading] = useState(false)
  const [isConverting, setIsConverting] = useState(false)
  const [progress, setProgress] = useState(0)
  const [file, setFile] = useState<File | null>(null)
  const [outputUrl, setOutputUrl] = useState<string | null>(null)
  const [outputFormat, setOutputFormat] = useState<string | null>(null)
  const [detectedFormat, setDetectedFormat] = useState<string | null>(null)

  // Create FFmpeg instance
  const ffmpegRef = useRef(new FFmpeg())
  const fileInputRef = useRef<HTMLInputElement | null>(null)
  const messageRef = useRef<HTMLParagraphElement | null>(null)

  // FFmpeg loading is now handled directly in the convertFile function

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      if (checkFileSize(selectedFile)) {
        setFile(selectedFile)
        setOutputUrl(null)

        // Detect format from file name
        const format = detectFormatFromFileName(selectedFile.name)
        setDetectedFormat(format)

        // Hide any previous messages
        const messageContainer = document.getElementById('message-container')
        if (messageRef.current && messageContainer) {
          messageContainer.classList.add('hidden')
          messageRef.current.innerHTML = ''
        }
      }
    }
  }

  const checkFileSize = (file: File): boolean => {
    const maxSizeMB = 500 // 500MB max file size
    const fileSizeMB = file.size / (1024 * 1024)

    if (fileSizeMB > maxSizeMB) {
      if (messageRef.current) {
        messageRef.current.innerHTML = `File is too large (${fileSizeMB.toFixed(
          2
        )}MB). Maximum file size is ${maxSizeMB}MB.`
        // Make the message visible with error styling
        const messageContainer = document.getElementById('message-container')
        if (messageContainer) {
          messageContainer.classList.remove('hidden')
          messageContainer.className = 'mt-4'
          messageRef.current.className = 'text-center text-sm text-red-600'
        }
      }
      return false
    }
    return true
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    const droppedFile = event.dataTransfer.files[0]
    if (droppedFile) {
      if (checkFileSize(droppedFile)) {
        setFile(droppedFile)
        setOutputUrl(null)

        // Detect format from file name
        const format = detectFormatFromFileName(droppedFile.name)
        setDetectedFormat(format)

        // Hide any previous messages
        const messageContainer = document.getElementById('message-container')
        if (messageRef.current && messageContainer) {
          messageContainer.classList.add('hidden')
          messageRef.current.innerHTML = ''
        }
      }
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
  }

  const convertFile = async () => {
    if (!file || !detectedFormat || !outputFormat) return

    try {
      setIsConverting(true)
      setProgress(0)
      setOutputUrl(null)

      // Load FFmpeg if not already loaded
      if (!ffmpegLoaded) {
        setIsFFmpegLoading(true)
        const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.10/dist/umd'
        const ffmpeg = ffmpegRef.current

        try {
          // Set up logging
          ffmpeg.on('log', ({ message }) => {
            if (messageRef.current) messageRef.current.innerHTML = message
            // console.log(message)
          })

          // Set up progress tracking
          ffmpeg.on('progress', ({ progress }) => {
            setProgress(Math.min(100, Math.round(progress * 100)))
          })

          // toBlobURL is used to bypass CORS issue
          await ffmpeg.load({
            coreURL: await toBlobURL(
              `${baseURL}/ffmpeg-core.js`,
              'text/javascript'
            ),
            wasmURL: await toBlobURL(
              `${baseURL}/ffmpeg-core.wasm`,
              'application/wasm'
            ),
          })

          setFFmpegLoaded(true)
          setIsFFmpegLoading(false)
        } catch (error) {
          console.error('Error loading FFmpeg:', error)
          if (messageRef.current) {
            messageRef.current.innerHTML =
              'Failed to load FFmpeg. Please try again or use a smaller file.'
            // Make the message visible with error styling
            const messageContainer =
              document.getElementById('message-container')
            if (messageContainer) {
              messageContainer.classList.remove('hidden')
              messageContainer.className =
                'mt-4 bg-red-50 p-4 rounded-md border border-red-200 mb-4'
            }
          }
          setIsFFmpegLoading(false)
          setIsConverting(false)
          return
        }
      }

      const ffmpeg = ffmpegRef.current

      // Check file size and warn if it's too large
      const fileSizeMB = file.size / (1024 * 1024)
      if (fileSizeMB > 200) {
        if (messageRef.current) {
          messageRef.current.innerHTML =
            'Warning: Large file detected. Processing may take longer or fail due to memory constraints.'
          // Make the message visible
          const messageContainer = document.getElementById('message-container')
          if (messageContainer) {
            messageContainer.classList.remove('hidden')
            messageContainer.className =
              'mt-4 bg-yellow-50 p-4 rounded-md border border-yellow-200 mb-4'
          }
        }
      }

      const inputFileName = `input.${detectedFormat}`
      const outputFileName = `output.${outputFormat}`

      try {
        // Write the input file to FFmpeg's virtual file system
        await ffmpeg.writeFile(inputFileName, await fetchFile(file))

        // Get the FFmpeg command for the conversion
        const ffmpegCommand = getFFmpegCommand(detectedFormat, outputFormat)
        // console.log('Running FFmpeg with command:', ffmpegCommand)

        // Execute the FFmpeg command
        await ffmpeg.exec(ffmpegCommand)

        // Read the output file
        const data = await ffmpeg.readFile(outputFileName)

        // Create a download URL for the output file
        const url = URL.createObjectURL(
          new Blob([data instanceof Uint8Array ? data : data], {
            type: getMimeType(outputFormat),
          })
        )

        setOutputUrl(url)

        // Show success message
        if (messageRef.current) {
          messageRef.current.innerHTML = t('tools.common.conversionSuccess')
          // Make the message visible with success styling
          const messageContainer = document.getElementById('message-container')
          if (messageContainer) {
            messageContainer.classList.remove('hidden')
            messageContainer.className =
              'mt-4 bg-green-50 p-4 rounded-md border border-green-200 mb-4'
          }
        }
      } catch (execError) {
        console.error('FFmpeg execution error:', execError)

        // Check if it's a memory error
        const errorMessage = String(execError)
        if (
          errorMessage.includes('memory') ||
          errorMessage.includes('allocation') ||
          errorMessage.includes('out of bound')
        ) {
          if (messageRef.current) {
            messageRef.current.innerHTML =
              "Memory error: The file is too large to process. We've optimized the conversion process to use less memory, but your file still exceeds the limits. Please try a smaller file, or try converting to a different format with lower quality settings."
            // Make the message visible with error styling
            const messageContainer =
              document.getElementById('message-container')
            if (messageContainer) {
              messageContainer.classList.remove('hidden')
              messageContainer.className =
                'mt-4 bg-red-50 p-4 rounded-md border border-red-200 mb-4'
            }
          }
        } else {
          if (messageRef.current) {
            messageRef.current.innerHTML = t('tools.common.conversionFailed')
            // Make the message visible with error styling
            const messageContainer =
              document.getElementById('message-container')
            if (messageContainer) {
              messageContainer.classList.remove('hidden')
              messageContainer.className =
                'mt-4 bg-red-50 p-4 rounded-md border border-red-200 mb-4'
            }
          }
        }
        throw execError // Re-throw to be caught by the outer catch
      }
    } catch (error) {
      console.error('Conversion error:', error)
      // The error message is already set in the inner catch block
    } finally {
      setIsConverting(false)
    }
  }
  // We don't need a separate FFmpeg loading UI anymore

  return (
    <div className="space-y-8">
      {/* File Upload Area */}
      <Card>
        <CardContent>
          {!file ? (
            <div
              className="border-muted rounded-lg border-2 border-dashed p-8 text-center"
              onDrop={handleDrop}
              onDragOver={handleDragOver}
            >
              <div className="flex flex-col items-center justify-center space-y-4">
                <div className="bg-muted rounded-full p-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-muted-foreground"
                  >
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="17 8 12 3 7 8"></polyline>
                    <line x1="12" y1="3" x2="12" y2="15"></line>
                  </svg>
                </div>
                <div>
                  <p className="text-muted-foreground text-sm">
                    {t('tools.common.dropzone')}
                  </p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2"
                  >
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="17 8 12 3 7 8"></polyline>
                    <line x1="12" y1="3" x2="12" y2="15"></line>
                  </svg>
                  {t('converters.universalFormatConverter.selectFile')}
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept={VIDEO_FORMATS.map(
                    (format) => `.${format.extension}`
                  ).join(',')}
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* File List Item */}
              <div className="bg-muted/20 flex items-center rounded-md border p-3">
                <div className="flex-1 overflow-hidden">
                  <p className="truncate font-medium">{file.name}</p>
                  <p className="text-muted-foreground text-xs">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="ml-2"
                  onClick={() => {
                    setFile(null)
                    setOutputUrl(null)
                    setDetectedFormat(null)
                  }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M18 6L6 18M6 6l12 12" />
                  </svg>
                  <span className="sr-only">Remove</span>
                </Button>
              </div>

              {/* Detected Format */}
              {detectedFormat && (
                <div className="flex items-center justify-center gap-2">
                  <span className="text-sm font-medium">
                    {t('converters.universalFormatConverter.detectedFormat')}:
                  </span>
                  <span className="rounded bg-blue-100 px-2 py-1 text-sm font-medium text-blue-800">
                    {detectedFormat.toUpperCase()}
                  </span>
                </div>
              )}

              {/* Output Format Selection */}
              <div className="mx-auto w-full max-w-xs">
                <label className="mb-2 block text-center text-sm font-medium">
                  {t('converters.universalFormatConverter.outputFormatLabel')}
                </label>
                <Select
                  value={outputFormat || undefined}
                  onValueChange={(value) => setOutputFormat(value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue
                      placeholder={t(
                        'converters.universalFormatConverter.selectOutputFormat'
                      )}
                    />
                  </SelectTrigger>
                  <SelectContent className="max-h-[300px] overflow-y-auto">
                    {/* Video Formats Group */}
                    <div className="text-muted-foreground px-2 py-1.5 text-sm font-semibold">
                      {t('formatGroups.videoFormats')}
                    </div>
                    {VIDEO_FORMATS.filter(
                      (format) =>
                        format.extension !== detectedFormat &&
                        !isAudioFormat(format.extension)
                    ).map((format) => (
                      <SelectItem
                        key={format.extension}
                        value={format.extension}
                      >
                        {format.name} (.{format.extension}) -{' '}
                        {format.description}
                      </SelectItem>
                    ))}

                    {/* Audio Formats Group */}
                    <div className="text-muted-foreground mt-2 px-2 py-1.5 text-sm font-semibold">
                      {t('formatGroups.audioFormats')}
                    </div>
                    {VIDEO_FORMATS.filter(
                      (format) =>
                        format.extension !== detectedFormat &&
                        isAudioFormat(format.extension)
                    ).map((format) => (
                      <SelectItem
                        key={format.extension}
                        value={format.extension}
                      >
                        {format.name} (.{format.extension}) -{' '}
                        {format.description}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-center gap-3 pt-2 max-lg:flex-col">
                <Button
                  onClick={convertFile}
                  disabled={isConverting || isFFmpegLoading || !detectedFormat}
                  className="h-auto overflow-hidden [word-break:break-word] whitespace-normal"
                >
                  {isConverting || isFFmpegLoading ? (
                    <>
                      <span className="mr-2 animate-spin">
                        <svg
                          viewBox="0 0 1024 1024"
                          focusable="false"
                          data-icon="loading"
                          width="1em"
                          height="1em"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path>
                        </svg>
                      </span>
                      {isFFmpegLoading
                        ? t('tools.common.ffmpegLoading')
                        : t('tools.common.processing')}
                    </>
                  ) : (
                    t('tools.common.convert')
                  )}
                </Button>
              </div>
            </div>
          )}

          {/* Progress Bar */}
          {isConverting && (
            <div className="mt-4 space-y-2">
              <Progress value={progress} />
              <p className="text-muted-foreground text-center text-xs">
                {progress}%
              </p>
            </div>
          )}

          {/* Download Button */}
          {outputUrl && (
            <div className="mt-6 text-center">
              <Button asChild>
                <a
                  href={outputUrl}
                  download={`${file?.name.split('.')[0] || 'file'}_converted.${outputFormat}`}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2"
                  >
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7 10 12 15 17 10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                  </svg>
                  {t('tools.common.download')}
                </a>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardContent>
          <h3 className="mb-2 text-lg font-medium">
            {t('tools.common.instructions')}
          </h3>
          <p className="text-muted-foreground text-sm">
            {t('converters.universalFormatConverter.instructions')}
          </p>
        </CardContent>
      </Card>

      {/* Status Messages */}
      <div id="message-container" className="mt-4 hidden">
        <p
          ref={messageRef}
          className="text-muted-foreground min-h-[1.5rem] text-sm"
        ></p>
      </div>
    </div>
  )
}
