import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import VideoTrimmerClient from '@/components/video-trimmer-client'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('tools.video-cutter')

  return {
    title: t('title'),
    description: t('description'),
  }
}

export default async function VideoCutterPage() {
  const t = await getTranslations('tools.video-cutter')

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">{t('title')}</h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            {t('description')}
          </p>
        </div>

        {/* Video Trimmer Component */}
        <VideoTrimmerClient />

        {/* Instructions */}
        <div className="mt-12 space-y-6">
          <h2 className="text-2xl font-semibold">How to Use the Video Trimmer</h2>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Step 1: Upload Video</h3>
              <p className="text-muted-foreground">
                Upload your video file by dragging and dropping it into the upload area, or click to select a file.
                Supported formats include MP4, WebM, OGG, AVI, and MOV (max 500MB).
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Step 2: Select Range</h3>
              <p className="text-muted-foreground">
                Use the timeline with thumbnails to select the start and end points of your desired clip.
                Drag the handles on the range slider to adjust the selection precisely.
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Step 3: Preview</h3>
              <p className="text-muted-foreground">
                Click "Preview Selection" to jump to the start of your selected range and review your clip
                before trimming. You can adjust the range as needed.
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Step 4: Trim & Download</h3>
              <p className="text-muted-foreground">
                Click "Trim Video" to process your selection. The trimmed video will be ready for download
                in MP4 format once processing is complete.
              </p>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-8">
            <h3 className="text-lg font-medium text-blue-800 mb-2">Features</h3>
            <ul className="text-blue-700 space-y-1 text-sm">
              <li>• Client-side processing - your videos never leave your device</li>
              <li>• Visual timeline with thumbnail previews</li>
              <li>• Precise time selection with drag handles</li>
              <li>• Real-time preview of selected range</li>
              <li>• Fast processing using stream copy when possible</li>
              <li>• Support for multiple video formats</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
