import { APP_NAME, SITE_URL } from './constants'

// Generate BreadcrumbList schema
export function generateBreadcrumbSchema(
  items: { name: string; url: string }[]
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  }
}

// Generate WebSite schema
export function generateWebsiteSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: APP_NAME,
    url: SITE_URL,
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${SITE_URL}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
  }
}

// Generate SoftwareApplication schema for tools
export function generateToolSchema(
  toolName: string,
  toolDescription: string,
  toolUrl: string,
  rating: number = 0,
  ratingCount: number = 0
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: toolName,
    description: toolDescription,
    applicationCategory: 'MultimediaApplication',
    operatingSystem: 'Any',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    aggregateRating:
      ratingCount > 0
        ? {
            '@type': 'AggregateRating',
            ratingValue: rating.toString(),
            ratingCount: ratingCount.toString(),
            bestRating: '5',
            worstRating: '1',
          }
        : undefined,
    url: toolUrl,
  }
}
