import { notFound } from 'next/navigation'
import { routing } from '@/i18n/routing'
import { getTranslations, setRequestLocale } from 'next-intl/server'
import type { Metadata } from 'next'

import { APP_NAME, SITE_URL } from '@/lib/constants'
import { ConverterPageClient } from '@/components/converter-page-client'
import { parseSlug, isValidFormatSlug } from '@/lib/format-utils'

// Generate static params for common format combinations
export function generateStaticParams() {
  // Generate common format combinations for static generation
  const commonCombinations = [
    // Video to video
    { slug: 'mp4-to-webm' },
    { slug: 'webm-to-mp4' },
    { slug: 'mov-to-mp4' },
    { slug: 'avi-to-mp4' },
    // Video to audio
    { slug: 'mp4-to-mp3' },
    { slug: 'webm-to-mp3' },
    // Audio to audio
    { slug: 'mp3-to-wav' },
    { slug: 'wav-to-mp3' },
    // Single format converters
    { slug: 'mp4-converter' },
    { slug: 'webm-converter' },
    { slug: 'mp3-converter' },
  ]

  return routing.locales
    .map((locale) => commonCombinations.map((combo) => ({ locale, ...combo })))
    .flat()
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string; locale: string }>
}): Promise<Metadata> {
  const { slug, locale } = await params
  const t = await getTranslations({ locale })

  // If the slug is not a valid format combination, return 404
  if (!isValidFormatSlug(slug)) {
    return {
      title: 'Format Not Found',
      description: 'The requested format converter could not be found.',
    }
  }

  // Parse the slug to get input and output formats
  const { inputFormat, outputFormat, isSingleFormat } = parseSlug(slug, locale)

  // Generate title and description based on formats
  let title, description

  if (isSingleFormat) {
    title = t('dynamicConverter.singleFormatTitle', {
      format: inputFormat.toUpperCase(),
    })
    description = t('dynamicConverter.singleFormatDescription', {
      format: inputFormat.toUpperCase(),
    })
  } else {
    title = t('dynamicConverter.title', {
      inputFormat: inputFormat.toUpperCase(),
      outputFormat: outputFormat.toUpperCase(),
    })
    description = t('dynamicConverter.description', {
      inputFormat: inputFormat.toUpperCase(),
      outputFormat: outputFormat.toUpperCase(),
    })
  }

  return {
    title: t('seo.title', {
      toolName: title,
      appName: APP_NAME,
    }),
    description: t('seo.description', {
      toolDescription: description,
    }),
    alternates: {
      canonical: `/${locale}/${slug}`,
      languages: Object.fromEntries(
        routing.locales.map((l) => [l, `/${l}/${slug}`])
      ),
    },
    openGraph: {
      type: 'website',
      title: t('seo.title', {
        toolName: title,
        appName: APP_NAME,
      }),
      description: t('seo.description', {
        toolDescription: description,
      }),
      url: `${SITE_URL}/${locale}/${slug}`,
      siteName: APP_NAME,
      images: [
        {
          url: `${SITE_URL}/og-image.jpg`,
          width: 1200,
          height: 630,
          alt: APP_NAME,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('seo.title', {
        toolName: title,
        appName: APP_NAME,
      }),
      description: t('seo.description', {
        toolDescription: description,
      }),
      images: [`${SITE_URL}/og-image.jpg`],
    },
  }
}

export default async function DynamicConverterPage({
  params,
}: {
  params: Promise<{ slug: string; locale: string }>
}) {
  // Access params directly
  const { slug, locale } = await params

  // Enable static rendering
  setRequestLocale(locale)

  // If the slug is not a valid format combination, return 404
  if (!isValidFormatSlug(slug)) {
    notFound()
  }

  // Parse the slug to get input and output formats
  const { inputFormat, outputFormat, isSingleFormat } = parseSlug(slug, locale)

  // Get translations for the title and description
  const t = await getTranslations('dynamicConverter')

  const title = isSingleFormat
    ? t('singleFormatTitle', {
        format: inputFormat.toUpperCase(),
      })
    : t('title', {
        inputFormat: inputFormat.toUpperCase(),
        outputFormat: outputFormat.toUpperCase(),
      })

  const description = isSingleFormat
    ? t('singleFormatDescription', {
        format: inputFormat.toUpperCase(),
      })
    : t('description', {
        inputFormat: inputFormat.toUpperCase(),
        outputFormat: outputFormat.toUpperCase(),
      })

  return (
    <ConverterPageClient
      slug={slug}
      inputFormat={inputFormat}
      outputFormat={outputFormat}
      isSingleFormat={isSingleFormat}
      title={title}
      description={description}
      locale={locale}
    />
  )
}
