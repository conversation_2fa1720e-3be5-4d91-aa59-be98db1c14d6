import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { getTranslations, setRequestLocale } from 'next-intl/server'
import { routing } from '@/i18n/routing'

import { MarkdownContent } from '@/components/markdown-content'
import { PageSchema } from '@/components/schema/page-schema'
import { getPageMarkdown, getAvailablePageMarkdowns } from '@/lib/markdown'
import { APP_NAME, SITE_URL } from '@/lib/constants'

// Define valid page slugs
const VALID_PAGES = ['about', 'contact', 'dmca', 'privacy', 'terms']

export async function generateStaticParams() {
  const pageSlugs = await getAvailablePageMarkdowns()

  return routing.locales
    .map((locale) =>
      pageSlugs
        .filter((slug) => VALID_PAGES.includes(slug))
        .map((page) => ({ locale, page }))
    )
    .flat()
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ page: string; locale: string }>
}): Promise<Metadata> {
  const { page, locale } = await params

  // Check if the page is valid
  if (!VALID_PAGES.includes(page)) {
    return {
      title: 'Page Not Found',
      description: 'The requested page could not be found.',
    }
  }

  // Get translations
  const t = await getTranslations({ locale, namespace: 'pages' })

  // Try to get metadata from markdown file first
  const markdown = await getPageMarkdown(page, locale)

  // If markdown exists, use its frontmatter for metadata
  if (markdown && markdown.frontmatter) {
    const { title, description, keywords } = markdown.frontmatter

    return {
      title: `${title} | ${APP_NAME}`,
      description: description,
      keywords: keywords,
      alternates: {
        canonical: `/${locale}/${page}`,
        languages: Object.fromEntries(
          routing.locales.map((l) => [l, `/${l}/${page}`])
        ),
      },
      openGraph: {
        type: 'website',
        title: `${title} | ${APP_NAME}`,
        description: description,
        url: `${SITE_URL}/${locale}/${page}`,
        siteName: APP_NAME,
        images: [
          {
            url: `${SITE_URL}/og-image.jpg`,
            width: 1200,
            height: 630,
            alt: APP_NAME,
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title: `${title} | ${APP_NAME}`,
        description: description,
        images: [`${SITE_URL}/og-image.jpg`],
      },
    }
  }

  // Fallback to translations if no markdown
  // Get translated title and description based on page slug
  let translatedTitle = ''
  let translatedDescription = ''

  // Use explicit keys based on page slug
  switch (page) {
    case 'about':
      translatedTitle = t('about.title')
      translatedDescription = t('about.description')
      break
    case 'contact':
      translatedTitle = t('contact.title')
      translatedDescription = t('contact.description')
      break
    case 'privacy':
      translatedTitle = t('privacy.title')
      translatedDescription = t('privacy.description')
      break
    case 'terms':
      translatedTitle = t('terms.title')
      translatedDescription = t('terms.description')
      break
    case 'dmca':
      translatedTitle = t('dmca.title')
      translatedDescription = t('dmca.description')
      break
    default:
      translatedTitle = 'Page'
      translatedDescription = 'Page description'
  }

  return {
    title: `${translatedTitle} | ${APP_NAME}`,
    description: translatedDescription,
    alternates: {
      canonical: `/${locale}/${page}`,
      languages: Object.fromEntries(
        routing.locales.map((l) => [l, `/${l}/${page}`])
      ),
    },
    openGraph: {
      type: 'website',
      title: `${translatedTitle} | ${APP_NAME}`,
      description: translatedDescription,
      url: `${SITE_URL}/${locale}/${page}`,
      siteName: APP_NAME,
      images: [
        {
          url: `${SITE_URL}/og-image.jpg`,
          width: 1200,
          height: 630,
          alt: APP_NAME,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: `${translatedTitle} | ${APP_NAME}`,
      description: translatedDescription,
      images: [`${SITE_URL}/og-image.jpg`],
    },
  }
}

export default async function StaticPage({
  params,
}: {
  params: Promise<{ page: string; locale: string }>
}) {
  const { page, locale } = await params

  // Enable static rendering
  setRequestLocale(locale)

  // Check if the page is valid
  if (!VALID_PAGES.includes(page)) {
    notFound()
  }

  // Get translations
  const t = await getTranslations({ locale, namespace: 'pages' })

  // Get markdown content
  const markdown = await getPageMarkdown(page, locale)

  // If no markdown content is available, return 404
  if (!markdown) {
    notFound()
  }

  // Get title and description from markdown frontmatter or translations
  let translatedTitle = ''
  let translatedDescription = ''

  // Use explicit keys based on page slug
  switch (page) {
    case 'about':
      translatedTitle = t('about.title')
      translatedDescription = t('about.description')
      break
    case 'contact':
      translatedTitle = t('contact.title')
      translatedDescription = t('contact.description')
      break
    case 'privacy':
      translatedTitle = t('privacy.title')
      translatedDescription = t('privacy.description')
      break
    case 'terms':
      translatedTitle = t('terms.title')
      translatedDescription = t('terms.description')
      break
    case 'dmca':
      translatedTitle = t('dmca.title')
      translatedDescription = t('dmca.description')
      break
    default:
      translatedTitle = 'Page'
      translatedDescription = 'Page description'
  }

  const title = markdown.frontmatter?.title || translatedTitle
  const description = markdown.frontmatter?.description || translatedDescription

  return (
    <div className="container mx-auto max-w-4xl">
      {/* Add Schema.org structured data */}
      <PageSchema
        slug={page}
        title={title}
        description={description}
        locale={locale}
      />

      {/* Markdown Content */}
      <div className="prose dark:prose-invert prose-headings:scroll-mt-20 max-w-none">
        <MarkdownContent content={markdown.content} />
      </div>
    </div>
  )
}
