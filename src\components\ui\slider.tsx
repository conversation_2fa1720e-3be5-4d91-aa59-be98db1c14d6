'use client'

import * as React from 'react'
import { cn } from '@/lib/utils'

interface SliderProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  label?: string
  value?: number
  min?: number
  max?: number
  step?: number
  onChange?: (value: number) => void
  showValue?: boolean
  valuePrefix?: string
  valueSuffix?: string
  className?: string
  labelClassName?: string
  trackClassName?: string
  thumbClassName?: string
  valueClassName?: string
}

export function Slider({
  label,
  value,
  min = 0,
  max = 100,
  step = 1,
  onChange,
  showValue = true,
  valuePrefix = '',
  valueSuffix = '',
  className,
  labelClassName,
  trackClassName,
  thumbClassName,
  valueClassName,
  ...props
}: SliderProps) {
  const [internalValue, setInternalValue] = React.useState<number>(
    value || min
  )

  React.useEffect(() => {
    if (value !== undefined) {
      setInternalValue(value)
    }
  }, [value])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = Number(e.target.value)
    setInternalValue(newValue)
    onChange?.(newValue)
  }

  // Calculate the percentage for styling the track
  const percentage = ((internalValue - min) / (max - min)) * 100

  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-center justify-between">
        {label && (
          <label className={cn('text-sm font-medium', labelClassName)}>
            {label}
          </label>
        )}
        {showValue && (
          <span
            className={cn(
              'text-muted-foreground text-xs font-medium',
              valueClassName
            )}
          >
            {valuePrefix}
            {internalValue}
            {valueSuffix}
          </span>
        )}
      </div>
      <div className="relative">
        <div
          className={cn(
            'bg-primary/20 absolute h-2 w-full rounded-full',
            trackClassName
          )}
        />
        <div
          className={cn('bg-primary absolute h-2 rounded-full')}
          style={{ width: `${percentage}%` }}
        />
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={internalValue}
          onChange={handleChange}
          className={cn(
            'relative h-2 w-full cursor-pointer appearance-none rounded-full bg-transparent',
            'focus:outline-none focus:ring-0 focus:ring-offset-0',
            '[&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-primary',
            '[&::-moz-range-thumb]:h-4 [&::-moz-range-thumb]:w-4 [&::-moz-range-thumb]:appearance-none [&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:border-0 [&::-moz-range-thumb]:bg-primary',
            thumbClassName
          )}
          {...props}
        />
      </div>
    </div>
  )
}
