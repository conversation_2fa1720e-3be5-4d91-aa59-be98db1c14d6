import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'

/**
 * Get the markdown content for a specific tool and locale
 * @param toolSlug The slug of the tool
 * @param locale The locale code (e.g., 'en', 'es')
 * @returns An object containing the markdown content and frontmatter
 */
export async function getToolMarkdown(toolSlug: string, locale: string) {
  // Define the path to the markdown file
  const filePath = path.join(
    process.cwd(),
    'src/content/tools',
    toolSlug,
    `${locale}.md`
  )

  // Check if the file exists
  if (!fs.existsSync(filePath)) {
    // If the file doesn't exist for the requested locale, fall back to English
    if (locale !== 'en') {
      return getToolMarkdown(toolSlug, 'en')
    }
    // If even the English file doesn't exist, return null
    return null
  }

  // Read the file content
  const fileContent = fs.readFileSync(filePath, 'utf8')

  // Parse the frontmatter and content
  const { data, content } = matter(fileContent)

  return {
    frontmatter: data,
    content,
  }
}

/**
 * Get the markdown content for a specific page and locale
 * @param pageSlug The slug of the page
 * @param locale The locale code (e.g., 'en', 'es')
 * @returns An object containing the markdown content and frontmatter
 */
export async function getPageMarkdown(pageSlug: string, locale: string) {
  // Define the path to the markdown file
  const filePath = path.join(
    process.cwd(),
    'src/content/pages',
    pageSlug,
    `${locale}.md`
  )

  // Check if the file exists
  if (!fs.existsSync(filePath)) {
    // If the file doesn't exist for the requested locale, fall back to English
    if (locale !== 'en') {
      return getPageMarkdown(pageSlug, 'en')
    }
    // If even the English file doesn't exist, return null
    return null
  }

  // Read the file content
  const fileContent = fs.readFileSync(filePath, 'utf8')

  // Parse the frontmatter and content
  const { data, content } = matter(fileContent)

  return {
    frontmatter: data,
    content,
  }
}

/**
 * Get a list of all available tool markdown files
 * @returns An array of tool slugs that have markdown files
 */
export async function getAvailableToolMarkdowns() {
  const toolsDir = path.join(process.cwd(), 'src/content/tools')

  // Check if the directory exists
  if (!fs.existsSync(toolsDir)) {
    return []
  }

  // Get all subdirectories in the tools directory
  const toolSlugs = fs
    .readdirSync(toolsDir, { withFileTypes: true })
    .filter((dirent) => dirent.isDirectory())
    .map((dirent) => dirent.name)

  return toolSlugs
}

/**
 * Get a list of all available page markdown files
 * @returns An array of page slugs that have markdown files
 */
export async function getAvailablePageMarkdowns() {
  const pagesDir = path.join(process.cwd(), 'src/content/pages')

  // Check if the directory exists
  if (!fs.existsSync(pagesDir)) {
    return []
  }

  // Get all subdirectories in the pages directory
  const pageSlugs = fs
    .readdirSync(pagesDir, { withFileTypes: true })
    .filter((dirent) => dirent.isDirectory())
    .map((dirent) => dirent.name)

  return pageSlugs
}

/**
 * Check if a markdown file exists for a specific tool and locale
 * @param toolSlug The slug of the tool
 * @param locale The locale code (e.g., 'en', 'es')
 * @returns A boolean indicating whether the markdown file exists
 */
export async function hasToolMarkdown(toolSlug: string, locale: string) {
  const filePath = path.join(
    process.cwd(),
    'src/content/tools',
    toolSlug,
    `${locale}.md`
  )
  return fs.existsSync(filePath)
}

/**
 * Check if a markdown file exists for a specific page and locale
 * @param pageSlug The slug of the page
 * @param locale The locale code (e.g., 'en', 'es')
 * @returns A boolean indicating whether the markdown file exists
 */
export async function hasPageMarkdown(pageSlug: string, locale: string) {
  const filePath = path.join(
    process.cwd(),
    'src/content/pages',
    pageSlug,
    `${locale}.md`
  )
  return fs.existsSync(filePath)
}
