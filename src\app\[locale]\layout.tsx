import type { Metada<PERSON> } from 'next'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google'
import { notFound } from 'next/navigation'
import { NextIntlClientProvider, hasLocale } from 'next-intl'
import {
  getMessages,
  getTranslations,
  setRequestLocale,
} from 'next-intl/server'
import { GoogleAnalytics } from '@next/third-parties/google'
import { Toaster } from '@/components/ui/sonner'
import { Providers } from '@/components/providers'

import { routing } from '@/i18n/routing'
import { APP_NAME, SITE_URL } from '@/lib/constants'
import '../globals.css'

import Header from '@/components/layout/header'
import Footer from '@/components/layout/footer'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  const t = await getTranslations({ locale, namespace: 'app' })

  return {
    title: {
      template: `%s | ${APP_NAME}`,
      default: APP_NAME,
    },
    description: t('description'),
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: `/${locale}`,
      languages: {
        en: '/en',
        es: '/es',
        fr: '/fr',
        pt: '/pt',
        zh: '/zh',
        de: '/de',
        ja: '/ja',
        ru: '/ru',
        it: '/it',
      },
    },
    openGraph: {
      type: 'website',
      siteName: APP_NAME,
      title: APP_NAME,
      description: t('description'),
      url: `${SITE_URL}/${locale}`,
      images: [
        {
          url: `${SITE_URL}/og-image.jpg`,
          width: 1200,
          height: 630,
          alt: APP_NAME,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: APP_NAME,
      description: t('description'),
      images: [`${SITE_URL}/og-image.jpg`],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    icons: {
      icon: 'images/favicon.ico',
      // shortcut: 'images/favicon-16x16.png',
      // apple: 'images/apple-touch-icon.png',
    },
  }
}

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }))
}

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode
  params: Promise<{ locale: string }>
}>) {
  // Ensure that the incoming `locale` is valid
  const { locale } = await params
  if (!hasLocale(routing.locales, locale)) {
    notFound()
  }

  // Enable static rendering
  setRequestLocale(locale)

  // Load messages for the current locale
  const messages = await getMessages({ locale })

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        <meta
          name="google-site-verification"
          content="w6x0ppiHsCbMOjsmOKf0x_v7DP3pj6F39UFeZMDb9bY"
        />
        <meta name="yandex-verification" content="9d11fb873dc5b5ad" />
        <meta name="msvalidate.01" content="601F6DCDE09C5AC7DAF34DAEAC811879" />
        {process.env.NODE_ENV !== 'development' && (
          <GoogleAnalytics gaId="G-LDS98Q6G48" />
        )}
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        <Providers>
          <NextIntlClientProvider locale={locale} messages={messages}>
            <div className="flex min-h-screen flex-col">
              <Header />
              <main className="container mx-auto flex-1 px-4 py-8">
                {children}
              </main>
              <Footer />
            </div>
            <Toaster />
          </NextIntlClientProvider>
        </Providers>
      </body>
    </html>
  )
}
