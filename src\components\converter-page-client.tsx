'use client'

import { useTranslations } from 'next-intl'
import { Link } from '@/i18n/navigation'
import { ChevronRight } from 'lucide-react'
import { ToolSchema } from '@/components/schema/tool-schema'
import NoSsrWrapper from './no-ssr-wrapper'
import { DynamicFormatConverter } from '@/components/dynamic-format-converter'

interface ConverterPageClientProps {
  slug: string
  inputFormat: string
  outputFormat: string
  isSingleFormat: boolean
  title: string
  description: string
  locale: string
}

export function ConverterPageClient({
  slug,
  inputFormat,
  outputFormat,
  isSingleFormat,
  title,
  description,
  locale,
}: ConverterPageClientProps) {
  const t = useTranslations()

  return (
    <div className="mx-auto max-w-3xl">
      {/* Breadcrumbs */}
      <nav className="text-muted-foreground mb-6 flex items-center text-sm">
        <Link href={`/${locale}`} className="hover:text-foreground">
          {t('nav.home')}
        </Link>
        <ChevronRight className="mx-2 h-4 w-4" />
        <Link href={`/${locale}/tools`} className="hover:text-foreground">
          {t('nav.tools')}
        </Link>
        <ChevronRight className="mx-2 h-4 w-4" />
        <span className="text-foreground font-medium">{title}</span>
      </nav>

      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold tracking-tight">{title}</h1>
        <p className="text-muted-foreground">{description}</p>
      </div>

      {/* Add Schema.org structured data */}
      <ToolSchema slug={slug} rating={4.8} ratingCount={35} />

      <NoSsrWrapper>
        <DynamicFormatConverter
          slug={slug}
          inputFormat={inputFormat}
          outputFormat={outputFormat}
          isSingleFormat={isSingleFormat}
        />
      </NoSsrWrapper>
    </div>
  )
}
