import { useTranslations } from 'next-intl'
import { getTranslations, setRequestLocale } from 'next-intl/server'
import { Link } from '@/i18n/navigation'
import { ChevronRight } from 'lucide-react'
import { routing } from '@/i18n/routing'
import type { Metadata } from 'next'

import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { TOOLS, TOOL_CATEGORIES, APP_NAME, SITE_URL } from '@/lib/constants'
import { ToolsSchema } from '@/components/schema/tools-schema'
import { use } from 'react'

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }))
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  const t = await getTranslations({ locale })

  return {
    title: t('nav.tools'),
    description: `${t('app.description')} - ${t('nav.tools')}`,
    alternates: {
      canonical: `/${locale}/tools`,
      languages: Object.fromEntries(
        routing.locales.map((l) => [l, `/${l}/tools`])
      ),
    },
    openGraph: {
      type: 'website',
      title: `${t('nav.tools')} | ${APP_NAME}`,
      description: `${t('app.description')} - ${t('nav.tools')}`,
      url: `${SITE_URL}/${locale}/tools`,
      siteName: APP_NAME,
      images: [
        {
          url: `${SITE_URL}/og-image.jpg`,
          width: 1200,
          height: 630,
          alt: APP_NAME,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: `${t('nav.tools')} | ${APP_NAME}`,
      description: `${t('app.description')} - ${t('nav.tools')}`,
      images: [`${SITE_URL}/og-image.jpg`],
    },
  }
}

export default function ToolsPage({
  params,
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = use(params)
  setRequestLocale(locale)

  const t = useTranslations()

  // Group tools by category
  const toolsByCategory = TOOL_CATEGORIES.map((category) => {
    return {
      ...category,
      tools: TOOLS.filter((tool) => tool.category === category.slug),
    }
  })

  return (
    <div className="space-y-8">
      {/* Breadcrumbs */}
      <nav className="text-muted-foreground flex items-center text-sm">
        <Link href={`/`} className="hover:text-foreground">
          {t('nav.home')}
        </Link>
        <ChevronRight className="mx-2 h-4 w-4" />
        <span className="text-foreground font-medium">{t('nav.tools')}</span>
      </nav>

      {/* Add Schema.org structured data */}
      <ToolsSchema />

      {/* Page Header */}
      <div className="py-8 text-center">
        <h1 className="mb-4 text-3xl font-bold tracking-tight md:text-4xl">
          {t('nav.tools')}
        </h1>
        <p className="text-muted-foreground mx-auto max-w-2xl text-lg">
          {t('app.description')}
        </p>
      </div>

      {/* Tools Section */}
      <section className="space-y-12">
        {toolsByCategory.map((category) => (
          <div key={category.slug} className="space-y-6">
            <h2 className="text-2xl font-bold tracking-tight">
              {t(`tools.categories.${category.slug}`)}
            </h2>
            <p className="text-muted-foreground mb-4">{category.description}</p>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {category.tools.map((tool) => (
                <Link
                  href={`/tools/${tool.slug}`}
                  key={tool.slug}
                  className="block transition-transform hover:scale-[1.02]"
                >
                  <Card className="hover:border-primary flex h-full cursor-pointer flex-col border-2">
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        {t(`tools.${tool.slug}.title`)}
                        <ChevronRight className="text-muted-foreground h-5 w-5" />
                      </CardTitle>
                      <CardDescription>
                        {t(`tools.${tool.slug}.description`)}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-1">
                      <p className="text-sm">
                        {t(`tools.${tool.slug}.instructions`)}
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button className="w-full">
                        {t('tools.common.convert')}
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        ))}
      </section>
    </div>
  )
}
