'use client'

import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { Link } from '@/i18n/navigation'
import { TOOLS, ToolSlugs } from '@/lib/constants'
import { Search } from 'lucide-react'
import { Input } from '@/components/ui/input'

interface ToolsSearchProps {
  currentToolSlug?: ToolSlugs
  onToolSelect?: () => void
}

export function ToolsSearch({ currentToolSlug, onToolSelect }: ToolsSearchProps) {
  const t = useTranslations()
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<Array<(typeof TOOLS)[number]>>([])
  const [isSearching, setIsSearching] = useState(false)

  // Filter tools based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setSearchResults([])
      setIsSearching(false)
      return
    }

    setIsSearching(true)
    const query = searchQuery.toLowerCase()
    
    const filteredTools = TOOLS.filter((tool) => {
      const title = t(`tools.${tool.slug}.title`).toLowerCase()
      const description = t(`tools.${tool.slug}.description`).toLowerCase()
      
      return (
        title.includes(query) || 
        description.includes(query) || 
        tool.slug.includes(query)
      )
    }).filter(tool => tool.slug !== currentToolSlug)
    
    setSearchResults(filteredTools)
  }, [searchQuery, t, currentToolSlug])

  return (
    <div className="mb-6">
      <div className="relative">
        <div className="absolute inset-y-0 left-2 flex items-center pointer-events-none">
          <Search className="h-4 w-4 text-muted-foreground" />
        </div>
        <Input
          type="search"
          placeholder={t('sidebar.searchTools')}
          className="pl-8 text-sm"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>
      
      {isSearching && searchResults.length > 0 && (
        <div className="mt-2 rounded-md border bg-card shadow-sm">
          <div className="py-1">
            {searchResults.slice(0, 5).map((tool) => (
              <Link
                key={tool.slug}
                href={`/tools/${tool.slug}`}
                className="block px-3 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
                onClick={() => {
                  setSearchQuery('')
                  onToolSelect?.()
                }}
              >
                {t(`tools.${tool.slug}.title`)}
              </Link>
            ))}
          </div>
        </div>
      )}
      
      {isSearching && searchQuery.trim() && searchResults.length === 0 && (
        <div className="mt-2 rounded-md border bg-card p-2 text-sm text-muted-foreground">
          {t('sidebar.noToolsFound')}
        </div>
      )}
    </div>
  )
}
