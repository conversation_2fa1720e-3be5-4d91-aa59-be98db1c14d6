'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Link } from '@/i18n/navigation'
import { Menu, Search, ChevronDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ThemeSwitcher } from '@/components/switchers/theme-switcher'
import { LanguageSwitcher } from '@/components/switchers/language-switcher'
import { APP_NAME, mainNavItems, navCategories } from '@/lib/constants'
import { useTranslations } from 'next-intl'
import {
  Sheet,
  <PERSON>et<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet'
import { MobileNavigationItems } from '@/components/navigation/menu-items'
import { SearchDialog } from '@/components/search-dialog'

export default function Header() {
  const [isOpen, setIsOpen] = useState(false)
  const [searchOpen, setSearchOpen] = useState(false)
  const [megaMenuOpen, setMegaMenuOpen] = useState(false)
  const megaMenuRef = useRef<HTMLDivElement>(null)
  const headerRef = useRef<HTMLElement>(null)
  const t = useTranslations()
  const ui = useTranslations('ui')

  // Close mega menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        megaMenuRef.current &&
        !megaMenuRef.current.contains(event.target as Node) &&
        !(headerRef.current && headerRef.current.contains(event.target as Node))
      ) {
        setMegaMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <header ref={headerRef} className="relative border-b">
      <div className="container mx-auto px-4 py-2 md:py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link href="/" className="text-xl font-bold tracking-tight">
              {APP_NAME}
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden items-center lg:flex">
            <nav className="flex items-center space-x-1">
              {/* Home Link */}
              <Link
                href="/"
                className="hover:bg-accent rounded-md px-4 py-2 text-sm font-medium"
              >
                {t('nav.home')}
              </Link>

              {/* Tools Link with Mega Menu */}
              <div className="relative" ref={megaMenuRef}>
                <button
                  onClick={() => setMegaMenuOpen(!megaMenuOpen)}
                  className={`hover:bg-accent flex items-center rounded-md px-4 py-2 text-sm font-medium ${megaMenuOpen ? 'bg-accent' : ''}`}
                >
                  {t('nav.tools')}
                  <ChevronDown
                    className={`ml-1 h-4 w-4 transition-transform ${megaMenuOpen ? 'rotate-180' : ''}`}
                  />
                </button>
              </div>
            </nav>

            <div className="ml-4 flex items-center gap-4">
              {/* Search Button */}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSearchOpen(true)}
                aria-label="Search"
              >
                <Search className="h-5 w-5" />
              </Button>

              {/* Language Switcher */}
              <LanguageSwitcher />

              {/* Theme Toggle */}
              <ThemeSwitcher />
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="flex items-center space-x-1 lg:hidden">
            {/* Search Button for Mobile */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSearchOpen(true)}
              aria-label="Search"
            >
              <Search className="h-5 w-5" />
            </Button>

            {/* Theme Toggle for Mobile */}
            <ThemeSwitcher />

            {/* Mobile Menu Button */}
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="h-12 w-12" />
                  <span className="sr-only">{ui('toggleMenu')}</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[300px] p-0">
                <SheetHeader className="border-b px-4 py-3">
                  <SheetTitle>{APP_NAME}</SheetTitle>
                </SheetHeader>
                <MobileNavigationItems
                  onClose={() => setIsOpen(false)}
                  mainItems={mainNavItems}
                  categories={navCategories}
                />
              </SheetContent>
            </Sheet>
          </div>

          {/* Search Dialog */}
          <SearchDialog open={searchOpen} onOpenChange={setSearchOpen} />
        </div>
      </div>

      {/* Mega Menu - Full Width */}
      {megaMenuOpen && (
        <div className="absolute top-full right-0 left-0 z-50 w-full">
          <div className="bg-popover w-full border-t shadow-lg">
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-1 gap-6 py-8 sm:grid-cols-2 md:grid-cols-4">
                {navCategories.Tools.map((category) => (
                  <div key={category.href} className="space-y-3">
                    <Link
                      href={category.href}
                      className="text-foreground hover:text-primary block border-b pb-2 text-base font-medium"
                    >
                      {t(category.translationKey)}
                    </Link>
                    <ul className="space-y-1">
                      {category.items.map((item) => (
                        <li key={item.href}>
                          <Link
                            href={item.href}
                            className="hover:text-primary block py-1 text-sm hover:underline"
                            onClick={() => setMegaMenuOpen(false)}
                          >
                            {t(item.translationKey)}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
              <div className="bg-muted/50 flex justify-center p-3">
                <Link
                  href="/tools"
                  className="text-primary hover:text-primary/80 text-sm font-medium"
                  onClick={() => setMegaMenuOpen(false)}
                >
                  {t('sidebar.allTools')}
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  )
}
