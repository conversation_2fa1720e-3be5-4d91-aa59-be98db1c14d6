/* Custom styles for Markdown tables */
.prose .markdown-table-wrapper {
  overflow-x: auto;
  margin: 1.5rem 0;
  border-radius: 0.5rem;
  border: 1px solid var(--border);
  max-width: 100%;
}

.prose .markdown-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
  margin: 0;
}

.prose .markdown-table thead {
  background-color: var(--secondary);
}

.prose .markdown-table th {
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  border-bottom: 1px solid var(--border);
  color: var(--foreground);
}

.prose .markdown-table td {
  padding: 0.75rem 1rem;
  border-top: 1px solid var(--border);
  vertical-align: top;
  color: var(--foreground);
}

.prose .markdown-table tr:nth-child(even) {
  background-color: var(--secondary);
}

.prose .markdown-table tr:hover {
  background-color: var(--muted);
}

/* Responsive adjustments for small screens */
@media (max-width: 640px) {
  .prose .markdown-table th,
  .prose .markdown-table td {
    padding: 0.5rem 0.75rem;
  }
}

/* Ensure tables don't overflow their container */
.prose table {
  table-layout: auto;
  width: 100%;
  margin: 0;
}

/* Add zebra striping for better readability */
.prose tbody tr:nth-child(odd) {
  background-color: transparent;
}

.prose tbody tr:nth-child(even) {
  background-color: var(--secondary);
}

/* Add some spacing between table cells */
.prose th,
.prose td {
  padding: 0.75rem 1rem;
}

/* Add a subtle hover effect */
.prose tbody tr:hover {
  background-color: var(--muted);
}
