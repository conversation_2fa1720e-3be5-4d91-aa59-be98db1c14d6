'use client'
import { useRef, useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile, toBlobURL } from '@ffmpeg/util'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { ToolSlugs } from '@/lib/constants'

interface ScreenRecorderProps {
  toolSlug?: ToolSlugs
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const ScreenRecorder = ({ toolSlug }: ScreenRecorderProps) => {
  const t = useTranslations()

  // Refs
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const chunksRef = useRef<Blob[]>([])
  const videoRef = useRef<HTMLVideoElement | null>(null)
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const ffmpegRef = useRef(new FFmpeg())
  const messageRef = useRef<HTMLParagraphElement | null>(null)

  // States
  const [isRecording, setIsRecording] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [recordingTime, setRecordingTime] = useState(0)
  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null)
  const [webmUrl, setWebmUrl] = useState<string | null>(null)
  const [mp4Url, setMp4Url] = useState<string | null>(null)
  const [isConverting, setIsConverting] = useState(false)
  // Keep progress state for internal tracking even though we don't display it
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [progress, setProgress] = useState(0)
  const [ffmpegLoaded, setFFmpegLoaded] = useState(false)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isFFmpegLoading, setIsFFmpegLoading] = useState(false)
  const [screenSource, setScreenSource] = useState<string>('screen')
  const [withAudio, setWithAudio] = useState(false)
  const [browserSupported, setBrowserSupported] = useState(true)

  // Check browser support
  useEffect(() => {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
      setBrowserSupported(false)
    }
  }, [])

  // Timer for recording duration
  useEffect(() => {
    if (isRecording && !isPaused) {
      timerRef.current = setInterval(() => {
        setRecordingTime((prevTime) => prevTime + 1)
      }, 1000)
    } else if (timerRef.current) {
      clearInterval(timerRef.current)
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }, [isRecording, isPaused])

  // Format time as MM:SS
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`
  }

  // Load FFmpeg
  const loadFFmpeg = async () => {
    setIsFFmpegLoading(true)
    const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.10/dist/umd'
    const ffmpeg = ffmpegRef.current

    try {
      // Set up logging
      ffmpeg.on('log', ({ message }) => {
        if (messageRef.current) messageRef.current.innerHTML = message
        // console.log('FFmpeg log:', message)
      })

      // Set up progress tracking
      ffmpeg.on('progress', ({ progress }) => {
        // Ensure progress is a valid number between 0 and 1
        const validProgress =
          typeof progress === 'number' && !isNaN(progress) && isFinite(progress)
            ? Math.max(0, Math.min(1, progress))
            : 0

        const progressPercent = Math.round(validProgress * 100)
        // console.log(`FFmpeg progress: ${progressPercent}%`)
        setProgress(progressPercent)
      })

      // toBlobURL is used to bypass CORS issue
      await ffmpeg.load({
        coreURL: await toBlobURL(
          `${baseURL}/ffmpeg-core.js`,
          'text/javascript'
        ),
        wasmURL: await toBlobURL(
          `${baseURL}/ffmpeg-core.wasm`,
          'application/wasm'
        ),
      })

      // console.log('FFmpeg loaded successfully')
      setFFmpegLoaded(true)
      setIsFFmpegLoading(false)
    } catch (error) {
      console.error('Error loading FFmpeg:', error)
      if (messageRef.current) {
        messageRef.current.innerHTML =
          'Failed to load FFmpeg. Please try again or use a smaller file.'
        // Make the message visible with error styling
        messageRef.current.parentElement?.classList.remove('hidden')
        messageRef.current.parentElement?.classList.add(
          'bg-red-50',
          'p-4',
          'rounded-md',
          'border',
          'border-red-200',
          'mb-4'
        )
      }
      setIsFFmpegLoading(false)
    }
  }

  // Start recording
  const startRecording = async () => {
    if (!browserSupported) return

    try {
      // Reset states
      setRecordingTime(0)
      setRecordedBlob(null)
      setWebmUrl(null)
      setMp4Url(null)
      chunksRef.current = []

      // Get display media stream
      const displayMediaOptions: DisplayMediaStreamOptions = {
        video: {
          displaySurface: screenSource as DisplayCaptureSurfaceType,
        },
        audio: withAudio,
      }

      const stream =
        await navigator.mediaDevices.getDisplayMedia(displayMediaOptions)
      streamRef.current = stream

      // If audio is enabled, get audio stream and add it
      if (withAudio) {
        try {
          const audioStream = await navigator.mediaDevices.getUserMedia({
            audio: true,
          })
          audioStream.getAudioTracks().forEach((track) => {
            stream.addTrack(track)
          })
        } catch (audioError) {
          console.warn('Could not get audio stream:', audioError)
        }
      }

      // Show preview
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.muted = true // Mute to prevent feedback
      }

      // Create MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'video/webm',
      })
      mediaRecorderRef.current = mediaRecorder

      // Handle data available event
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data)
        }
      }

      // Handle recording stop
      mediaRecorder.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: 'video/webm' })
        setRecordedBlob(blob)
        const url = URL.createObjectURL(blob)
        setWebmUrl(url)

        // Update video source
        if (videoRef.current) {
          videoRef.current.srcObject = null
          videoRef.current.src = url
          videoRef.current.muted = false // Unmute for playback
        }

        // Stop all tracks
        if (streamRef.current) {
          streamRef.current.getTracks().forEach((track) => track.stop())
          streamRef.current = null
        }

        // Show success message
        if (messageRef.current) {
          messageRef.current.innerHTML = t(
            'tools.screen-recorder.recordingReady'
          )
          // Make the message visible with success styling
          messageRef.current.parentElement?.classList.remove('hidden')
          messageRef.current.parentElement?.classList.add(
            'bg-green-50',
            'p-4',
            'rounded-md',
            'border',
            'border-green-200',
            'mb-4'
          )
        }
      }

      // Start recording
      mediaRecorder.start(1000) // Collect data every second
      setIsRecording(true)
    } catch (error) {
      console.error('Error starting recording:', error)
      if (messageRef.current) {
        messageRef.current.innerHTML = t('tools.screen-recorder.noPermission')
        // Make the message visible with error styling
        messageRef.current.parentElement?.classList.remove('hidden')
        messageRef.current.parentElement?.classList.add(
          'bg-red-50',
          'p-4',
          'rounded-md',
          'border',
          'border-red-200',
          'mb-4'
        )
      }
    }
  }

  // Stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
      setIsPaused(false)
    }
  }

  // Pause recording
  const pauseRecording = () => {
    if (mediaRecorderRef.current && isRecording && !isPaused) {
      mediaRecorderRef.current.pause()
      setIsPaused(true)
    }
  }

  // Resume recording
  const resumeRecording = () => {
    if (mediaRecorderRef.current && isRecording && isPaused) {
      mediaRecorderRef.current.resume()
      setIsPaused(false)
    }
  }

  // Convert WebM to MP4
  const convertToMP4 = async () => {
    if (!recordedBlob) {
      console.error('No recorded blob available for conversion')
      return
    }

    // Make sure FFmpeg is loaded
    if (!ffmpegLoaded) {
      // console.log('FFmpeg not loaded, loading now...')
      try {
        await loadFFmpeg()
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (loadError) {
        // console.error('Failed to load FFmpeg:', loadError)
        if (messageRef.current) {
          messageRef.current.innerHTML =
            'Failed to load FFmpeg. Please try again.'
          messageRef.current.parentElement?.classList.remove('hidden')
          messageRef.current.parentElement?.classList.add(
            'bg-red-50',
            'p-4',
            'rounded-md',
            'border',
            'border-red-200',
            'mb-4'
          )
        }
        return
      }
    }

    try {
      setIsConverting(true)
      setProgress(0) // Reset progress to 0
      setMp4Url(null)

      // Re-register progress handler to ensure it's working correctly
      const ffmpeg = ffmpegRef.current

      // Use a handler function to properly remove the event listener
      const progressHandler = ({ progress }: { progress: number }) => {
        // Ensure progress is a valid number between 0 and 1
        const validProgress =
          typeof progress === 'number' && !isNaN(progress) && isFinite(progress)
            ? Math.max(0, Math.min(1, progress))
            : 0

        const progressPercent = Math.round(validProgress * 100)
        // console.log(`Conversion progress: ${progressPercent}%`)
        setProgress(progressPercent)
      }

      // Remove any existing handlers and add the new one
      ffmpeg.off('progress', progressHandler)
      ffmpeg.on('progress', progressHandler)

      // console.log('Starting WebM to MP4 conversion...')
      // console.log('Blob size:', recordedBlob.size, 'bytes')

      // Show message that conversion is starting
      if (messageRef.current) {
        messageRef.current.innerHTML = `
          <div class="flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Converting WebM to MP4... This may take a moment depending on the video length.</span>
          </div>
        `
        messageRef.current.parentElement?.classList.remove('hidden')
        messageRef.current.parentElement?.classList.add(
          'bg-blue-50',
          'p-4',
          'rounded-md',
          'border',
          'border-blue-200',
          'mb-4'
        )
      }

      // Write the WebM file to FFmpeg's virtual file system
      // console.log('Writing WebM file to FFmpeg virtual filesystem...')
      const fileData = await fetchFile(recordedBlob)
      await ffmpeg.writeFile('input.webm', fileData)
      // console.log('WebM file written successfully')

      // Use a simpler conversion command with more compatible options
      // console.log('Executing FFmpeg conversion command...')
      await ffmpeg.exec([
        '-i',
        'input.webm',
        '-c:v',
        'libx264',
        '-preset',
        'ultrafast', // Use ultrafast preset for better browser compatibility
        '-crf',
        '23',
        '-pix_fmt',
        'yuv420p', // Ensure compatibility with most players
        '-movflags',
        '+faststart', // Optimize for web playback
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        'output.mp4',
      ])
      // console.log('FFmpeg conversion command completed')

      // Read the output file
      // console.log('Reading output MP4 file...')
      const data = await ffmpeg.readFile('output.mp4')

      // Create a blob from the data regardless of its type
      const blob = new Blob([data], { type: 'video/mp4' })
      // console.log(
      //   'Output MP4 file read successfully, size:',
      //   blob.size,
      //   'bytes'
      // )

      if (!data || blob.size === 0) {
        throw new Error('Output file is empty or could not be read')
      }

      const url = URL.createObjectURL(blob)
      // console.log('Created object URL for MP4 blob:', url)

      // Set progress to 100% when conversion is complete
      setProgress(100)
      setMp4Url(url)

      // Show success message
      if (messageRef.current) {
        messageRef.current.innerHTML = t('tools.common.conversionSuccess')
        // Make the message visible with success styling
        messageRef.current.parentElement?.classList.remove('hidden')
        messageRef.current.parentElement?.classList.add(
          'bg-green-50',
          'p-4',
          'rounded-md',
          'border',
          'border-green-200',
          'mb-4'
        )
      }
    } catch (error) {
      console.error('Error converting to MP4:', error)
      if (messageRef.current) {
        messageRef.current.innerHTML =
          t('tools.screen-recorder.conversionFailed') +
          ' Error: ' +
          (error instanceof Error ? error.message : String(error))
        // Make the message visible with error styling
        messageRef.current.parentElement?.classList.remove('hidden')
        messageRef.current.parentElement?.classList.add(
          'bg-red-50',
          'p-4',
          'rounded-md',
          'border',
          'border-red-200',
          'mb-4'
        )
      }
    } finally {
      setIsConverting(false)
    }
  }

  return (
    <div className="space-y-8">
      {!browserSupported ? (
        <Card>
          <CardContent>
            <div className="rounded-md border border-red-200 bg-red-50 p-4 text-center">
              <p className="text-red-700">
                {t('tools.screen-recorder.browserNotSupported')}
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Video Preview */}
          <Card>
            <CardContent>
              <div className="bg-muted aspect-video overflow-hidden rounded-md">
                <video
                  ref={videoRef}
                  className="h-full w-full object-contain"
                  autoPlay
                  playsInline
                  controls={!!webmUrl}
                />
              </div>

              {/* Status Messages (shown when needed) */}
              <div className="mt-4 hidden">
                <p
                  ref={messageRef}
                  className="text-muted-foreground min-h-[1.5rem] text-sm"
                ></p>
              </div>

              {/* Recording Time */}
              {isRecording && (
                <div className="mt-4 flex items-center justify-center space-x-2">
                  <div className="h-3 w-3 animate-pulse rounded-full bg-red-500" />
                  <p className="text-sm font-medium">
                    {t('tools.screen-recorder.recordingTime')}:{' '}
                    {formatTime(recordingTime)}
                  </p>
                </div>
              )}

              {/* Recording Controls */}
              <div className="mt-6 flex flex-wrap justify-center gap-3">
                {!isRecording && !webmUrl && (
                  <>
                    <div className="mb-4 w-full space-y-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="screen-source">
                          {t('tools.screen-recorder.selectScreen')}
                        </Label>
                        <Select
                          value={screenSource}
                          onValueChange={setScreenSource}
                        >
                          <SelectTrigger
                            id="screen-source"
                            className="w-[180px]"
                          >
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="screen">
                              {t('tools.screen-recorder.entireScreen')}
                            </SelectItem>
                            <SelectItem value="window">
                              {t('tools.screen-recorder.currentWindow')}
                            </SelectItem>
                            <SelectItem value="browser">
                              {t('tools.screen-recorder.currentTab')}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="with-audio"
                          checked={withAudio}
                          onCheckedChange={setWithAudio}
                        />
                        <Label htmlFor="with-audio">
                          {t('tools.screen-recorder.withAudio')}
                        </Label>
                      </div>
                    </div>

                    <Button onClick={startRecording}>
                      {t('tools.screen-recorder.startRecording')}
                    </Button>
                  </>
                )}

                {isRecording && !isPaused && (
                  <>
                    <Button variant="outline" onClick={pauseRecording}>
                      {t('tools.screen-recorder.pauseRecording')}
                    </Button>
                    <Button variant="destructive" onClick={stopRecording}>
                      {t('tools.screen-recorder.stopRecording')}
                    </Button>
                  </>
                )}

                {isRecording && isPaused && (
                  <>
                    <Button variant="outline" onClick={resumeRecording}>
                      {t('tools.screen-recorder.resumeRecording')}
                    </Button>
                    <Button variant="destructive" onClick={stopRecording}>
                      {t('tools.screen-recorder.stopRecording')}
                    </Button>
                  </>
                )}

                {webmUrl && !isConverting && (
                  <>
                    <Button asChild variant="outline">
                      <a
                        href={webmUrl}
                        download={`screen-recording_${Date.now()}_converted.webm`}
                      >
                        {t('tools.screen-recorder.downloadWebM')}
                      </a>
                    </Button>
                    {!mp4Url && (
                      <Button onClick={convertToMP4} disabled={isConverting}>
                        {isConverting ? (
                          <>
                            <span className="mr-2 inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em]"></span>
                            {t('tools.common.processing')}
                          </>
                        ) : (
                          t('tools.screen-recorder.convertToMP4')
                        )}
                      </Button>
                    )}
                    {mp4Url && (
                      <Button asChild>
                        <a
                          href={mp4Url}
                          download={`screen-recording_${Date.now()}_converted.mp4`}
                        >
                          {t('tools.screen-recorder.downloadMP4')}
                        </a>
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      onClick={() => {
                        setWebmUrl(null)
                        setMp4Url(null)
                        setRecordedBlob(null)
                        if (videoRef.current) {
                          videoRef.current.src = ''
                        }
                      }}
                    >
                      {t('tools.common.changeFile')}
                    </Button>
                  </>
                )}
              </div>

              {/* Conversion Progress */}
              {isConverting && (
                <div className="mt-4">
                  {/* Spinner Loader */}
                  <div className="flex flex-col items-center justify-center">
                    <div className="relative h-12 w-12">
                      <div className="border-primary absolute inset-0 h-full w-full animate-spin rounded-full border-4 border-solid border-t-transparent"></div>
                    </div>
                    <p className="mt-2 text-center text-sm font-medium">
                      {t('tools.common.processing')}
                    </p>
                    <p className="text-muted-foreground mt-1 text-center text-xs">
                      This may take a moment depending on the video length
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Instructions */}
          <Card>
            <CardContent>
              <h3 className="mb-2 text-lg font-medium">
                {t('tools.common.instructions')}
              </h3>
              <p className="text-muted-foreground text-sm">
                {t('tools.screen-recorder.instructions')}
              </p>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
