import React, { useState } from 'react'
import { Star } from 'lucide-react'
import { cn } from '@/lib/utils'

interface RatingProps {
  defaultValue?: number
  max?: number
  onChange?: (value: number) => void
  className?: string
  readOnly?: boolean
}

export function Rating({
  defaultValue = 0,
  max = 5,
  onChange,
  className,
  readOnly = false,
}: RatingProps) {
  const [rating, setRating] = useState(defaultValue)
  const [hoverRating, setHoverRating] = useState(0)

  const handleClick = (value: number) => {
    if (readOnly) return
    setRating(value)
    onChange?.(value)
  }

  return (
    <div className={cn('flex items-center', className)}>
      {Array.from({ length: max }).map((_, index) => {
        const value = index + 1
        const filled = hoverRating ? value <= hoverRating : value <= rating

        return (
          <Star
            key={index}
            className={cn(
              'h-5 w-5 cursor-pointer transition-colors',
              filled ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300',
              readOnly && 'cursor-default'
            )}
            onClick={() => handleClick(value)}
            onMouseEnter={() => !readOnly && setHoverRating(value)}
            onMouseLeave={() => !readOnly && setHoverRating(0)}
          />
        )
      })}
    </div>
  )
}
