import { Link } from '@/i18n/navigation'
import { But<PERSON> } from '@/components/ui/button'
import type { Metadata } from 'next'
import { useTranslations } from 'next-intl'
import { getTranslations } from 'next-intl/server'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('error.notFound')

  return {
    title: t('title'),
    description: t('description'),
    robots: {
      index: false,
      follow: false,
    },
  }
}

export default function NotFound() {
  const t = useTranslations('error.notFound')

  return (
    <div className="flex min-h-[60vh] flex-col items-center justify-center text-center">
      <h1 className="mb-4 text-6xl font-bold">404</h1>
      <h2 className="mb-6 text-2xl font-semibold">{t('heading')}</h2>
      <p className="text-muted-foreground mb-8 max-w-md">{t('description')}</p>
      <Button asChild>
        <Link href="/">{t('goHome')}</Link>
      </Button>
    </div>
  )
}
