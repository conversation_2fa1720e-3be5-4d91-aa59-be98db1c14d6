import { VIDEO_FORMATS } from './video-formats';

// Define translation mappings for the word "to" in different languages
const TO_TRANSLATIONS: Record<string, string[]> = {
  en: ['to'],
  es: ['a', 'para'],
};

// Check if a format is an audio format
export function isAudioFormat(format: string): boolean {
  const audioFormats = ['mp3', 'wav', 'aac', 'ogg', 'flac', 'ac3', 'wma'];
  return audioFormats.includes(format);
}

// Check if a format is a video format
export function isVideoFormat(format: string): boolean {
  return !isAudioFormat(format);
}

// Check if a format is valid
export function isValidFormat(format: string): boolean {
  return VIDEO_FORMATS.some((f) => f.extension === format);
}

// Parse the slug to extract input and output formats
export function parseSlug(slug: string, locale: string = 'en'): {
  inputFormat: string;
  outputFormat: string;
  isSingleFormat: boolean;
} {
  // Get the translations for "to" in the current locale
  const toTranslations = TO_TRANSLATIONS[locale] || TO_TRANSLATIONS.en;
  
  // Check if the slug contains any of the "to" translations
  const toRegex = new RegExp(`-(${toTranslations.join('|')})-(\\w+)$`);
  const match = slug.match(toRegex);
  
  if (match) {
    // If the slug contains a "to" translation, extract the input and output formats
    const inputFormat = slug.split('-')[0];
    const outputFormat = match[2];
    return {
      inputFormat,
      outputFormat,
      isSingleFormat: false,
    };
  } else {
    // If the slug doesn't contain a "to" translation, check if it's a single format
    const formatMatch = slug.match(/^(\w+)-converter$/);
    if (formatMatch) {
      const format = formatMatch[1];
      return {
        inputFormat: format,
        outputFormat: format,
        isSingleFormat: true,
      };
    }
    
    // If it's not a single format, try to extract formats directly
    const parts = slug.split('-');
    if (parts.length >= 3 && parts[1] === 'to') {
      return {
        inputFormat: parts[0],
        outputFormat: parts[2],
        isSingleFormat: false,
      };
    }
    
    // Default fallback
    return {
      inputFormat: 'mp4',
      outputFormat: 'mp4',
      isSingleFormat: true,
    };
  }
}

// Check if a slug is a valid format combination
export function isValidFormatSlug(slug: string, locale: string = 'en'): boolean {
  // Parse the slug
  const { inputFormat, outputFormat, isSingleFormat } = parseSlug(slug, locale);
  
  // Check if both formats are valid
  const isInputValid = isValidFormat(inputFormat);
  const isOutputValid = isValidFormat(outputFormat);
  
  // If it's a single format, only the input format needs to be valid
  if (isSingleFormat) {
    return isInputValid;
  }
  
  // If it's a format combination, both formats need to be valid
  // Also, if the input is audio, the output must also be audio
  if (isAudioFormat(inputFormat) && !isAudioFormat(outputFormat)) {
    return false;
  }
  
  return isInputValid && isOutputValid;
}

// Generate a slug from input and output formats
export function generateFormatSlug(
  inputFormat: string,
  outputFormat: string,
  locale: string = 'en'
): string {
  // If input and output are the same, return a single format converter slug
  if (inputFormat === outputFormat) {
    return `${inputFormat}-converter`;
  }
  
  // Get the translation for "to" in the current locale
  const toTranslation = TO_TRANSLATIONS[locale]?.[0] || 'to';
  
  // Generate the slug
  return `${inputFormat}-${toTranslation}-${outputFormat}`;
}

// Get all supported formats
export function getAllFormats(): string[] {
  return VIDEO_FORMATS.map((format) => format.extension);
}

// Get all supported video formats
export function getVideoFormats(): string[] {
  return VIDEO_FORMATS
    .filter((format) => !isAudioFormat(format.extension))
    .map((format) => format.extension);
}

// Get all supported audio formats
export function getAudioFormats(): string[] {
  return VIDEO_FORMATS
    .filter((format) => isAudioFormat(format.extension))
    .map((format) => format.extension);
}

// Get valid output formats for a given input format
export function getValidOutputFormats(inputFormat: string): string[] {
  // If the input is an audio format, only return audio formats
  if (isAudioFormat(inputFormat)) {
    return getAudioFormats();
  }
  
  // If the input is a video format, return both video and audio formats
  return getAllFormats();
}

// Get format name from extension
export function getFormatName(extension: string): string {
  const format = VIDEO_FORMATS.find((f) => f.extension === extension);
  return format ? format.name : extension.toUpperCase();
}

// Get format description from extension
export function getFormatDescription(extension: string): string {
  const format = VIDEO_FORMATS.find((f) => f.extension === extension);
  return format ? format.description : '';
}
