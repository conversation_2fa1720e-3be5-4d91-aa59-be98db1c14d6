'use client'

import React, { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { Link } from '@/i18n/navigation'
import {
  ArrowRight,
  CheckCircle,
  Zap,
  Shield,
  FileVideo,
  FileAudio,
  ArrowRightLeft,
  Download,
  Upload,
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { SearchDialog } from '@/components/search-dialog'
import { cn } from '@/lib/utils'

// File conversion animation states - labels will be replaced with translations
const conversionSteps = [
  { id: 'upload', label: 'Upload' },
  { id: 'converting', label: 'Converting' },
  { id: 'download', label: 'Download' },
]

// File formats for animation
const fileFormats = [
  { from: 'MP4', to: 'MP3', fromIcon: FileVideo, toIcon: FileAudio },
  { from: 'WebM', to: 'MP4', fromIcon: FileVideo, toIcon: FileVideo },
  { from: 'MOV', to: 'MP4', fromIcon: FileVideo, toIcon: FileVideo },
  { from: 'MP3', to: 'WAV', fromIcon: FileAudio, toIcon: FileAudio },
]

// Client component for the hero section with animation
export function HomeHero() {
  const t = useTranslations()
  const [searchOpen, setSearchOpen] = useState(false)
  const [activeStep, setActiveStep] = useState(0)
  const [activeFormat, setActiveFormat] = useState(0)

  // Animation effect for conversion steps
  useEffect(() => {
    const stepInterval = setInterval(() => {
      setActiveStep((current) => (current + 1) % conversionSteps.length)
    }, 2000)

    return () => clearInterval(stepInterval)
  }, [])

  // Animation effect for file formats
  useEffect(() => {
    const formatInterval = setInterval(() => {
      setActiveFormat((current) => (current + 1) % fileFormats.length)
    }, 6000)

    return () => clearInterval(formatInterval)
  }, [])

  return (
    <section className="border-b py-12">
      <div className="mx-auto px-4">
        <div className="grid items-center gap-8 md:grid-cols-2">
          {/* Left column: Text content */}
          <div className="flex flex-col">
            <div className="bg-primary/5 text-primary mb-2 inline-flex w-fit items-center rounded-full px-3 py-1 text-xs font-medium">
              <span className="mr-1">✨</span> {t('app.features.free')}
            </div>

            <h1 className="text-3xl font-bold tracking-tight sm:text-5xl">
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {t('app.hero.title.part1')}
              </span>
              <span className="mt-2 block">{t('app.hero.title.part2')}</span>
            </h1>

            <p className="text-muted-foreground mt-4 max-w-xl text-lg">
              {t('app.hero.description')}
            </p>

            {/* Feature list */}
            <div className="mt-6 flex flex-wrap gap-3">
              <div className="bg-background flex items-center space-x-2 rounded-full px-3 py-1 shadow-sm">
                <CheckCircle className="text-primary h-4 w-4" />
                <span className="text-sm font-medium">
                  {t('app.features.easy')}
                </span>
              </div>
              <div className="bg-background flex items-center space-x-2 rounded-full px-3 py-1 shadow-sm">
                <Zap className="text-primary h-4 w-4" />
                <span className="text-sm font-medium">
                  {t('app.features.fast')}
                </span>
              </div>
              <div className="bg-background flex items-center space-x-2 rounded-full px-3 py-1 shadow-sm">
                <Shield className="text-primary h-4 w-4" />
                <span className="text-sm font-medium">
                  {t('app.features.secure')}
                </span>
              </div>
            </div>

            {/* CTA button */}
            <div className="mt-8">
              <Button asChild size="lg" className="px-8 py-6 text-base">
                <Link href="/tools">
                  {t('app.hero.cta')}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>

          {/* Right column: File Conversion Animation */}
          <div className="relative hidden h-[350px] md:block">
            <div className="bg-card absolute inset-0 overflow-hidden rounded-lg border shadow-md">
              {/* Conversion animation container */}
              <div className="flex h-full flex-col items-center justify-center p-6">
                {/* Current format display */}
                <div className="mb-8 text-center">
                  <div className="text-lg font-medium">
                    {t('dynamicConverter.title', {
                      inputFormat: fileFormats[activeFormat].from,
                      outputFormat: fileFormats[activeFormat].to,
                    })}
                  </div>
                  <div className="text-muted-foreground text-sm">
                    {t('dynamicConverter.description', {
                      inputFormat: fileFormats[activeFormat].from,
                      outputFormat: fileFormats[activeFormat].to,
                    })}
                  </div>
                </div>

                {/* Conversion process animation */}
                <div className="relative flex w-full max-w-md items-center justify-between">
                  {/* Source file */}
                  <div
                    className={cn(
                      'flex flex-col items-center transition-all duration-500',
                      activeStep > 0
                        ? 'scale-90 opacity-50'
                        : 'scale-105 opacity-100'
                    )}
                  >
                    <div
                      className={cn(
                        'relative mb-2 rounded-lg border-2 p-4 transition-all duration-500',
                        activeStep === 0
                          ? 'border-blue-500 bg-white shadow-[0_0_15px_rgba(59,130,246,0.5)] dark:bg-gray-800'
                          : 'bg-background border-gray-200 shadow-sm dark:border-gray-700'
                      )}
                    >
                      <div
                        className={cn(
                          'transition-all duration-500',
                          activeStep === 0 ? 'scale-110' : ''
                        )}
                      >
                        {React.createElement(
                          fileFormats[activeFormat].fromIcon,
                          {
                            className: cn(
                              'h-12 w-12 transition-all duration-500',
                              activeStep === 0
                                ? 'text-blue-500'
                                : 'text-gray-400 dark:text-gray-500'
                            ),
                          }
                        )}
                      </div>
                      {activeStep === 0 && (
                        <div className="absolute -top-2 -right-2 flex h-6 w-6 items-center justify-center rounded-full bg-blue-500 text-white shadow-md">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="14"
                            height="14"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="3"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="17 8 12 3 7 8"></polyline>
                            <line x1="12" y1="3" x2="12" y2="15"></line>
                          </svg>
                        </div>
                      )}
                    </div>
                    <div className="flex flex-col items-center">
                      <span
                        className={cn(
                          'text-sm font-medium transition-all duration-300',
                          activeStep === 0 ? 'text-blue-500' : ''
                        )}
                      >
                        {fileFormats[activeFormat].from}
                      </span>
                      {activeStep === 0 && (
                        <span className="mt-1 rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                          {t('app.animation.original')}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Conversion arrow */}
                  <div className="mx-4 flex flex-col items-center">
                    <div
                      className={cn(
                        'rounded-full p-3 transition-all duration-300',
                        activeStep === 1
                          ? 'scale-125 border-2 border-purple-500 bg-purple-100 shadow-[0_0_10px_rgba(168,85,247,0.4)] dark:bg-purple-900/30'
                          : 'border border-gray-200 bg-gray-100 dark:border-gray-700 dark:bg-gray-800'
                      )}
                    >
                      <ArrowRightLeft
                        className={cn(
                          'h-6 w-6 transition-all duration-300',
                          activeStep === 1
                            ? 'text-purple-500'
                            : 'text-gray-400 dark:text-gray-500'
                        )}
                      />
                    </div>
                    <span
                      className={cn(
                        'mt-2 text-xs font-medium transition-all duration-300',
                        activeStep === 1
                          ? 'text-purple-500'
                          : 'text-gray-500 dark:text-gray-400'
                      )}
                    >
                      {activeStep === 1
                        ? t('app.animation.converting')
                        : conversionSteps[activeStep].label}
                    </span>
                  </div>

                  {/* Target file */}
                  <div
                    className={cn(
                      'flex flex-col items-center transition-all duration-500',
                      activeStep < 2
                        ? 'scale-90 opacity-50'
                        : 'scale-110 opacity-100'
                    )}
                  >
                    <div
                      className={cn(
                        'relative mb-2 rounded-lg border-2 p-4 transition-all duration-500',
                        activeStep === 2
                          ? 'border-green-500 bg-white shadow-[0_0_15px_rgba(34,197,94,0.5)] dark:bg-gray-800'
                          : 'bg-background border-gray-200 shadow-sm dark:border-gray-700'
                      )}
                    >
                      <div
                        className={cn(
                          'transition-all duration-500',
                          activeStep === 2 ? 'scale-110' : ''
                        )}
                      >
                        {React.createElement(fileFormats[activeFormat].toIcon, {
                          className: cn(
                            'h-12 w-12 transition-all duration-500',
                            activeStep === 2
                              ? 'text-green-500'
                              : 'text-gray-400 dark:text-gray-500'
                          ),
                        })}
                      </div>
                      {activeStep === 2 && (
                        <div className="absolute -top-2 -right-2 flex h-6 w-6 items-center justify-center rounded-full bg-green-500 text-white shadow-md">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="14"
                            height="14"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="3"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <polyline points="20 6 9 17 4 12"></polyline>
                          </svg>
                        </div>
                      )}
                    </div>
                    <div className="flex flex-col items-center">
                      <span
                        className={cn(
                          'text-sm font-medium transition-all duration-300',
                          activeStep === 2 ? 'text-green-500' : ''
                        )}
                      >
                        {fileFormats[activeFormat].to}
                      </span>
                      {activeStep === 2 && (
                        <span className="mt-1 rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-700 dark:bg-green-900 dark:text-green-300">
                          {t('app.animation.converted')}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Progress steps */}
                <div className="mt-8 flex w-full max-w-xs justify-between">
                  {conversionSteps.map((step, index) => {
                    // Get step-specific styles
                    const getStepStyles = (
                      idx: number,
                      currentStep: number
                    ) => {
                      if (idx === currentStep) {
                        if (idx === 0) {
                          return {
                            circle:
                              'border-blue-500 bg-blue-100 text-blue-500 dark:bg-blue-900/30 shadow-sm',
                            text: 'text-blue-500 font-medium',
                          }
                        } else if (idx === 1) {
                          return {
                            circle:
                              'border-purple-500 bg-purple-100 text-purple-500 dark:bg-purple-900/30 shadow-sm',
                            text: 'text-purple-500 font-medium',
                          }
                        } else {
                          return {
                            circle:
                              'border-green-500 bg-green-100 text-green-500 dark:bg-green-900/30 shadow-sm',
                            text: 'text-green-500 font-medium',
                          }
                        }
                      } else if (idx < currentStep) {
                        return {
                          circle:
                            'border-gray-300 bg-gray-100 text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400',
                          text: 'text-gray-500 dark:text-gray-400',
                        }
                      } else {
                        return {
                          circle:
                            'border-gray-200 bg-white text-gray-400 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-500',
                          text: 'text-gray-500 dark:text-gray-400',
                        }
                      }
                    }

                    const styles = getStepStyles(index, activeStep)

                    return (
                      <div key={step.id} className="flex flex-col items-center">
                        <div
                          className={cn(
                            'flex h-8 w-8 items-center justify-center rounded-full border-2 transition-all duration-300',
                            styles.circle
                          )}
                        >
                          {index === 0 && <Upload className="h-4 w-4" />}
                          {index === 1 && (
                            <ArrowRightLeft className="h-4 w-4" />
                          )}
                          {index === 2 && <Download className="h-4 w-4" />}
                        </div>
                        <span
                          className={cn(
                            'mt-1 text-xs transition-all duration-300',
                            styles.text
                          )}
                        >
                          {index === 0 && t('tools.common.upload')}
                          {index === 1 && t('app.animation.converting')}
                          {index === 2 && t('tools.common.download')}
                        </span>
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search Dialog */}
      <SearchDialog open={searchOpen} onOpenChange={setSearchOpen} />
    </section>
  )
}
