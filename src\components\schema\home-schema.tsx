'use client'

import { useEffect } from 'react'
import { useTranslations, useLocale } from 'next-intl'
import { APP_NAME, SITE_URL } from '@/lib/constants'

export function HomeSchema() {
  const t = useTranslations()
  const locale = useLocale()

  useEffect(() => {
    // Website Schema
    const websiteScript = document.createElement('script')
    websiteScript.type = 'application/ld+json'
    websiteScript.id = 'website-schema'

    const websiteSchema = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: APP_NAME,
      description: t('app.description'),
      url: `${SITE_URL}/${locale}`,
      potentialAction: {
        '@type': 'SearchAction',
        target: {
          '@type': 'EntryPoint',
          urlTemplate: `${SITE_URL}/${locale}/search?q={search_term_string}`,
        },
        'query-input': 'required name=search_term_string',
      },
    }

    websiteScript.innerHTML = JSON.stringify(websiteSchema)
    document.head.appendChild(websiteScript)

    // Organization Schema
    const orgScript = document.createElement('script')
    orgScript.type = 'application/ld+json'
    orgScript.id = 'organization-schema'

    const orgSchema = {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: APP_NAME,
      url: SITE_URL,
      logo: `${SITE_URL}/logo.png`,
      sameAs: [
        `https://twitter.com/${APP_NAME}`,
        `https://facebook.com/${APP_NAME}`,
        `https://instagram.com/${APP_NAME}`,
      ],
    }

    orgScript.innerHTML = JSON.stringify(orgSchema)
    document.head.appendChild(orgScript)

    // Breadcrumb Schema for Home
    const breadcrumbScript = document.createElement('script')
    breadcrumbScript.type = 'application/ld+json'
    breadcrumbScript.id = 'breadcrumb-schema'

    const breadcrumbSchema = {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: t('nav.home'),
          item: `${SITE_URL}/${locale}`,
        },
      ],
    }

    breadcrumbScript.innerHTML = JSON.stringify(breadcrumbSchema)
    document.head.appendChild(breadcrumbScript)

    return () => {
      const existingWebsiteScript = document.getElementById('website-schema')
      if (existingWebsiteScript) {
        existingWebsiteScript.remove()
      }

      const existingOrgScript = document.getElementById('organization-schema')
      if (existingOrgScript) {
        existingOrgScript.remove()
      }

      const existingBreadcrumbScript =
        document.getElementById('breadcrumb-schema')
      if (existingBreadcrumbScript) {
        existingBreadcrumbScript.remove()
      }
    }
  }, [t, locale])

  return null
}
