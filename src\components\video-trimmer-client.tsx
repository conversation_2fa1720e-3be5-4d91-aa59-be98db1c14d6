'use client'

import dynamic from 'next/dynamic'

const VideoTrimmer = dynamic(() => import('./video-trimmer').then(mod => ({ default: mod.VideoTrimmer })), {
  ssr: false,
  loading: () => (
    <div className="flex h-[400px] items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      <span className="ml-2">Loading video trimmer...</span>
    </div>
  ),
})

export default function VideoTrimmerClient() {
  return <VideoTrimmer />
}
