'use client'
import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile, toBlobURL } from '@ffmpeg/util'
import { useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { useTranslations } from 'next-intl'

export const Mp4ToMp3Converter = () => {
  const t = useTranslations()
  const [loaded, setLoaded] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isConverting, setIsConverting] = useState(false)
  const [progress, setProgress] = useState(0)
  const [file, setFile] = useState<File | null>(null)
  const [outputUrl, setOutputUrl] = useState<string | null>(null)
  const ffmpegRef = useRef(new FFmpeg())
  const fileInputRef = useRef<HTMLInputElement | null>(null)
  const messageRef = useRef<HTMLParagraphElement | null>(null)

  const load = async () => {
    setIsLoading(true)
    const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.10/dist/umd'
    const ffmpeg = ffmpegRef.current

    // Set up logging
    ffmpeg.on('log', ({ message }) => {
      if (messageRef.current) messageRef.current.innerHTML = message
      // console.log(message)
    })

    // Set up progress tracking
    ffmpeg.on('progress', ({ progress }) => {
      setProgress(Math.round(progress * 100))
    })

    // toBlobURL is used to bypass CORS issue
    await ffmpeg.load({
      coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
      wasmURL: await toBlobURL(
        `${baseURL}/ffmpeg-core.wasm`,
        'application/wasm'
      ),
    })

    setLoaded(true)
    setIsLoading(false)
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      setFile(selectedFile)
      setOutputUrl(null)
    }
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    const droppedFile = event.dataTransfer.files[0]
    if (droppedFile && droppedFile.type.includes('video/mp4')) {
      setFile(droppedFile)
      setOutputUrl(null)
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
  }

  const convertToMp3 = async () => {
    if (!file) return

    try {
      setIsConverting(true)
      setProgress(0)
      setOutputUrl(null)

      const ffmpeg = ffmpegRef.current

      // Write the input file to FFmpeg's virtual file system
      await ffmpeg.writeFile('input.mp4', await fetchFile(file))

      // Execute the FFmpeg command to convert MP4 to MP3
      await ffmpeg.exec([
        '-i',
        'input.mp4',
        '-vn',
        '-acodec',
        'libmp3lame',
        '-q:a',
        '2',
        'output.mp3',
      ])

      // Read the output file
      const data = (await ffmpeg.readFile('output.mp3')) as any

      // Create a download URL for the output file
      const url = URL.createObjectURL(
        new Blob([data.buffer], { type: 'audio/mpeg' })
      )

      setOutputUrl(url)
    } catch (error) {
      console.error('Conversion error:', error)
      if (messageRef.current) {
        messageRef.current.innerHTML = 'Conversion failed. Please try again.'
      }
    } finally {
      setIsConverting(false)
    }
  }

  if (!loaded) {
    return (
      <div className="flex flex-col items-center justify-center space-y-4 py-8">
        <h2 className="text-xl font-semibold">MP4 to MP3 Converter</h2>
        <p className="text-muted-foreground mb-4 text-center">
          {t('tools.mp4-to-mp3.description')}
        </p>
        <Button
          onClick={load}
          disabled={isLoading}
          className="flex items-center"
        >
          {isLoading ? (
            <>
              <span className="mr-2 animate-spin">
                <svg
                  viewBox="0 0 1024 1024"
                  focusable="false"
                  data-icon="loading"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path>
                </svg>
              </span>
              Loading FFmpeg...
            </>
          ) : (
            'Load FFmpeg to Start'
          )}
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div
        className="rounded-lg border-2 border-dashed p-8 text-center"
        onDrop={handleDrop}
        onDragOver={handleDragOver}
      >
        {!file ? (
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="bg-muted rounded-full p-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-muted-foreground"
              >
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="17 8 12 3 7 8"></polyline>
                <line x1="12" y1="3" x2="12" y2="15"></line>
              </svg>
            </div>
            <p className="text-muted-foreground text-sm">
              {t('tools.common.dropzone')}
            </p>
            <Button
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
            >
              Select MP4 File
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept=".mp4,video/mp4"
              onChange={handleFileChange}
              className="hidden"
            />
          </div>
        ) : (
          <div className="space-y-4">
            <p className="font-medium">{file.name}</p>
            <p className="text-muted-foreground text-sm">
              {(file.size / 1024 / 1024).toFixed(2)} MB
            </p>
            <div className="flex justify-center space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  setFile(null)
                  setOutputUrl(null)
                }}
              >
                Change File
              </Button>
              <Button onClick={convertToMp3} disabled={isConverting}>
                {isConverting ? (
                  <>
                    <span className="mr-2 animate-spin">
                      <svg
                        viewBox="0 0 1024 1024"
                        focusable="false"
                        data-icon="loading"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path>
                      </svg>
                    </span>
                    {t('tools.common.processing')}
                  </>
                ) : (
                  t('tools.common.convert')
                )}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Progress Bar */}
      {isConverting && (
        <div className="space-y-2">
          <Progress value={progress} />
          <p className="text-muted-foreground text-center text-xs">
            {progress}%
          </p>
        </div>
      )}

      {/* Download Button */}
      {outputUrl && (
        <div className="text-center">
          <Button asChild>
            <a href={outputUrl} download="converted.mp3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2"
              >
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
              </svg>
              {t('tools.common.download')}
            </a>
          </Button>
        </div>
      )}

      {/* Log Messages */}
      <div className="mt-4">
        <p ref={messageRef} className="text-muted-foreground text-sm"></p>
      </div>
    </div>
  )
}
