// Define video formats with their extensions, names, and MIME types
export interface VideoFormat {
  extension: string
  name: string
  mimeType: string
  description: string
}

export const VIDEO_FORMATS: VideoFormat[] = [
  // Video formats
  {
    extension: '3gp',
    name: '3GP',
    mimeType: 'video/3gpp',
    description: 'Third Generation Partnership Project',
  },
  {
    extension: 'asf',
    name: 'ASF',
    mimeType: 'video/x-ms-asf',
    description: 'Advanced Systems Format',
  },
  {
    extension: 'avi',
    name: 'AVI',
    mimeType: 'video/x-msvideo',
    description: 'Audio Video Interleave',
  },
  {
    extension: 'f4v',
    name: 'F4V',
    mimeType: 'video/mp4',
    description: 'Flash Video',
  },
  {
    extension: 'flv',
    name: 'FLV',
    mimeType: 'video/x-flv',
    description: 'Flash Video',
  },
  {
    extension: 'hevc',
    name: 'HEVC',
    mimeType: 'video/mp4',
    description: 'High Efficiency Video Coding',
  },
  {
    extension: 'm2ts',
    name: 'M2<PERSON>',
    mimeType: 'video/mp2t',
    description: 'MPEG-2 Transport Stream',
  },
  {
    extension: 'm2v',
    name: 'M2V',
    mimeType: 'video/mpeg',
    description: 'MPEG-2 Video',
  },
  {
    extension: 'm4v',
    name: 'M4V',
    mimeType: 'video/mp4',
    description: 'MPEG-4 Video',
  },
  {
    extension: 'mjpeg',
    name: 'MJPEG',
    mimeType: 'video/x-motion-jpeg',
    description: 'Motion JPEG',
  },
  {
    extension: 'mkv',
    name: 'MKV',
    mimeType: 'video/x-matroska',
    description: 'Matroska Multimedia Container',
  },
  {
    extension: 'mov',
    name: 'MOV',
    mimeType: 'video/quicktime',
    description: 'Apple QuickTime Movie',
  },
  {
    extension: 'mp4',
    name: 'MP4',
    mimeType: 'video/mp4',
    description: 'MPEG-4 Part 14',
  },
  {
    extension: 'mpeg',
    name: 'MPEG',
    mimeType: 'video/mpeg',
    description: 'Moving Picture Experts Group Phase 1 (MPEG-1)',
  },
  {
    extension: 'mpg',
    name: 'MPG',
    mimeType: 'video/mpeg',
    description: 'Moving Picture Experts Group',
  },
  {
    extension: 'mts',
    name: 'MTS',
    mimeType: 'video/mp2t',
    description: 'MPEG-2 Transport Stream',
  },
  {
    extension: 'mxf',
    name: 'MXF',
    mimeType: 'application/mxf',
    description: 'Material Exchange Format',
  },
  {
    extension: 'ogv',
    name: 'OGV',
    mimeType: 'video/ogg',
    description: 'Ogg Video File',
  },
  {
    extension: 'rm',
    name: 'RM',
    mimeType: 'application/vnd.rn-realmedia',
    description: 'RealMedia',
  },
  {
    extension: 'swf',
    name: 'SWF',
    mimeType: 'application/x-shockwave-flash',
    description: 'Small Web Format',
  },
  {
    extension: 'ts',
    name: 'TS',
    mimeType: 'video/mp2t',
    description: 'Transport Stream',
  },
  {
    extension: 'vob',
    name: 'VOB',
    mimeType: 'video/dvd',
    description: 'DVD-Video Object',
  },
  {
    extension: 'webm',
    name: 'WEBM',
    mimeType: 'video/webm',
    description: 'WEB Media',
  },
  {
    extension: 'wmv',
    name: 'WMV',
    mimeType: 'video/x-ms-wmv',
    description: 'Windows Media Video',
  },
  {
    extension: 'wtv',
    name: 'WTV',
    mimeType: 'video/x-ms-wtv',
    description: 'Windows Recorded TV Show',
  },

  // Audio formats
  {
    extension: 'mp3',
    name: 'MP3',
    mimeType: 'audio/mpeg',
    description: 'MPEG Audio Layer III',
  },
  {
    extension: 'wav',
    name: 'WAV',
    mimeType: 'audio/wav',
    description: 'Waveform Audio File Format',
  },
  {
    extension: 'aac',
    name: 'AAC',
    mimeType: 'audio/aac',
    description: 'Advanced Audio Coding',
  },
  {
    extension: 'ogg',
    name: 'OGG',
    mimeType: 'audio/ogg',
    description: 'Ogg Vorbis Audio',
  },
  {
    extension: 'flac',
    name: 'FLAC',
    mimeType: 'audio/flac',
    description: 'Free Lossless Audio Codec',
  },
  {
    extension: 'ac3',
    name: 'AC3',
    mimeType: 'audio/ac3',
    description: 'Dolby Digital Audio',
  },
  {
    extension: 'wma',
    name: 'WMA',
    mimeType: 'audio/x-ms-wma',
    description: 'Windows Media Audio',
  },
]

// Get FFmpeg command for converting between formats
export function getFFmpegCommand(
  inputFormat: string,
  outputFormat: string
): string[] {
  const baseCommand = ['-i', `input.${inputFormat}`]

  // Add format-specific options based on output format
  switch (outputFormat) {
    // Video formats
    case 'mp4':
      return [
        ...baseCommand,
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-preset',
        'medium',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        '-pix_fmt',
        'yuv420p',
        `output.${outputFormat}`,
      ]
    case 'webm':
      return [
        ...baseCommand,
        '-c:v',
        'libvpx-vp9',
        '-crf',
        '30',
        '-b:v',
        '0',
        '-b:a',
        '128k',
        '-c:a',
        'libopus',
        `output.${outputFormat}`,
      ]
    case 'avi':
      return [
        ...baseCommand,
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-c:a',
        'aac',
        '-strict',
        'experimental',
        '-b:a',
        '128k',
        `output.${outputFormat}`,
      ]
    case 'mov':
      return [
        ...baseCommand,
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-preset',
        'medium',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        '-pix_fmt',
        'yuv420p',
        `output.${outputFormat}`,
      ]
    case 'mkv':
      return [
        ...baseCommand,
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-preset',
        'medium',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        `output.${outputFormat}`,
      ]
    case 'flv':
      return [
        ...baseCommand,
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-preset',
        'medium',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        '-ar',
        '44100',
        `output.${outputFormat}`,
      ]
    case 'wmv':
      return [
        ...baseCommand,
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-preset',
        'medium',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        `output.${outputFormat}`,
      ]
    case '3gp':
      return [
        ...baseCommand,
        '-c:v',
        'libx264',
        '-profile:v',
        'baseline',
        '-level',
        '3.0',
        '-pix_fmt',
        'yuv420p',
        '-c:a',
        'aac',
        '-b:a',
        '96k',
        '-ar',
        '44100',
        '-ac',
        '1',
        `output.${outputFormat}`,
      ]
    case 'ogv':
      return [
        ...baseCommand,
        '-c:v',
        'libtheora',
        '-q:v',
        '7',
        '-c:a',
        'libvorbis',
        '-q:a',
        '4',
        `output.${outputFormat}`,
      ]
    case 'mpg':
    case 'mpeg':
      return [
        ...baseCommand,
        '-c:v',
        'mpeg2video',
        '-q:v',
        '5',
        '-c:a',
        'mp2',
        '-b:a',
        '192k',
        `output.${outputFormat}`,
      ]
    case 'm4v':
      return [
        ...baseCommand,
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-preset',
        'medium',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        '-pix_fmt',
        'yuv420p',
        `output.${outputFormat}`,
      ]
    case 'ts':
    case 'm2ts':
    case 'mts':
      return [
        ...baseCommand,
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-preset',
        'medium',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        '-f',
        'mpegts',
        `output.${outputFormat}`,
      ]
    case 'vob':
      return [
        ...baseCommand,
        '-target',
        'pal-dvd',
        '-c:v',
        'mpeg2video',
        '-c:a',
        'ac3',
        '-b:a',
        '192k',
        `output.${outputFormat}`,
      ]
    case 'asf':
      return [
        ...baseCommand,
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-preset',
        'medium',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        `output.${outputFormat}`,
      ]
    case 'f4v':
      return [
        ...baseCommand,
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-preset',
        'medium',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        '-f',
        'flv',
        `output.${outputFormat}`,
      ]
    case 'hevc':
      return [
        ...baseCommand,
        '-c:v',
        'libx265',
        '-crf',
        '28',
        '-preset',
        'medium',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        `output.${outputFormat}`,
      ]
    case 'm2v':
      return [
        ...baseCommand,
        '-c:v',
        'mpeg2video',
        '-q:v',
        '5',
        '-f',
        'mpeg2video',
        `output.${outputFormat}`,
      ]
    case 'mjpeg':
      return [
        ...baseCommand,
        '-c:v',
        'mjpeg',
        '-q:v',
        '3',
        '-an',
        `output.${outputFormat}`,
      ]
    case 'mxf':
      return [
        ...baseCommand,
        '-c:v',
        'mpeg2video',
        '-b:v',
        '50M',
        '-c:a',
        'pcm_s16le',
        '-f',
        'mxf',
        `output.${outputFormat}`,
      ]
    case 'rm':
      return [
        ...baseCommand,
        '-c:v',
        'rv20',
        '-c:a',
        'ac3',
        '-f',
        'rm',
        `output.${outputFormat}`,
      ]
    case 'swf':
      return [
        ...baseCommand,
        '-c:v',
        'flv',
        '-c:a',
        'adpcm_swf',
        '-f',
        'swf',
        `output.${outputFormat}`,
      ]
    case 'wtv':
      return [
        ...baseCommand,
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-preset',
        'medium',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        `output.${outputFormat}`,
      ]

    // Image/Animation formats
    case 'gif':
      return [
        ...baseCommand,
        '-vf',
        'fps=10,scale=320:-1:flags=lanczos,split[s0][s1];[s0]palettegen=max_colors=128:stats_mode=diff[p];[s1][p]paletteuse=dither=bayer:bayer_scale=5:diff_mode=rectangle',
        '-loop',
        '0',
        `output.${outputFormat}`,
      ]

    // Audio formats
    case 'mp3':
      return [
        ...baseCommand,
        '-vn',
        '-acodec',
        'libmp3lame',
        '-q:a',
        '2',
        `output.${outputFormat}`,
      ]
    case 'wav':
      return [
        ...baseCommand,
        '-vn',
        '-acodec',
        'pcm_s16le',
        `output.${outputFormat}`,
      ]
    case 'aac':
      return [
        ...baseCommand,
        '-vn',
        '-c:a',
        'aac',
        '-b:a',
        '192k',
        `output.${outputFormat}`,
      ]
    case 'ogg':
      return [
        ...baseCommand,
        '-vn',
        '-c:a',
        'libvorbis',
        '-q:a',
        '4',
        `output.${outputFormat}`,
      ]
    case 'flac':
      return [...baseCommand, '-vn', '-c:a', 'flac', `output.${outputFormat}`]
    case 'ac3':
      return [
        ...baseCommand,
        '-vn',
        '-c:a',
        'ac3',
        '-b:a',
        '192k',
        `output.${outputFormat}`,
      ]
    case 'wma':
      return [
        ...baseCommand,
        '-vn',
        '-c:a',
        'wmav2',
        '-b:a',
        '192k',
        `output.${outputFormat}`,
      ]

    default:
      // Fallback to a more robust generic conversion with common settings
      return [
        ...baseCommand,
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-preset',
        'medium',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        `output.${outputFormat}`,
      ]
  }
}

// Get MIME type for a given format extension
export function getMimeType(extension: string): string {
  const format = VIDEO_FORMATS.find((f) => f.extension === extension)
  return format?.mimeType || 'application/octet-stream'
}

// Get all supported input formats as a comma-separated string
export function getAllInputFormats(): string {
  return VIDEO_FORMATS.map((format) => format.extension).join(',')
}

// Detect format from file extension
export function detectFormatFromFileName(fileName: string): string | null {
  const extension = fileName.split('.').pop()?.toLowerCase()
  if (!extension) return null

  const format = VIDEO_FORMATS.find((f) => f.extension === extension)
  return format ? format.extension : null
}
