import { notFound } from 'next/navigation'
import { routing } from '@/i18n/routing'
import { getTranslations, setRequestLocale } from 'next-intl/server'
import { Link } from '@/i18n/navigation'
import { ChevronRight } from 'lucide-react'
import type { Metadata } from 'next'

import { EnhancedToolSchema } from '@/components/schema/enhanced-tool-schema'
import { FFmpegConverter } from '@/components/ffmpeg-converter'
import { ScreenRecorder } from '@/components/screen-recorder'
import { TOOLS, APP_NAME, SITE_URL, ToolSlugs } from '@/lib/constants'
import NoSSRWrapper from '@/components/no-ssr-wrapper'
import { MarkdownContent } from '@/components/markdown-content'
import { getToolMarkdown } from '@/lib/markdown'
import { RelatedToolsSidebar } from '@/components/related-tools-sidebar'

export function generateStaticParams() {
  return routing.locales
    .map((locale) => TOOLS.map((tool) => ({ locale, slug: tool.slug })))
    .flat()
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: ToolSlugs; locale: string }>
}): Promise<Metadata> {
  const { slug, locale } = await params
  const t = await getTranslations({ locale })

  // Find the tool by slug
  const tool = TOOLS.find((t) => t.slug === slug)

  // If tool not found, return default metadata
  if (!tool) {
    return {
      title: 'Tool Not Found',
      description: 'The requested tool could not be found.',
    }
  }

  // Try to get metadata from markdown file first
  const markdown = await getToolMarkdown(slug, locale)

  // If markdown exists, use its frontmatter for metadata
  if (markdown && markdown.frontmatter) {
    const { title, description, keywords } = markdown.frontmatter

    return {
      title: title || t(`tools.${slug}.title`),
      description:
        description ||
        t('seo.description', {
          toolDescription: t(`tools.${slug}.description`),
        }),
      keywords: keywords,
      alternates: {
        canonical: `/${locale}/tools/${slug}`,
        languages: Object.fromEntries(
          routing.locales.map((l) => [l, `/${l}/tools/${slug}`])
        ),
      },
      openGraph: {
        type: 'website',
        title:
          `${title} | ${APP_NAME}` ||
          t('seo.title', {
            toolName: t(`tools.${slug}.title`),
            appName: APP_NAME,
          }),
        description:
          description ||
          t('seo.description', {
            toolDescription: t(`tools.${slug}.description`),
          }),
        url: `${SITE_URL}/${locale}/tools/${slug}`,
        siteName: APP_NAME,
        images: [
          {
            url: `${SITE_URL}/og-image.jpg`,
            width: 1200,
            height: 630,
            alt: title || t(`tools.${slug}.title`),
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title:
          `${title} | ${APP_NAME}` ||
          t('seo.title', {
            toolName: t(`tools.${slug}.title`),
            appName: APP_NAME,
          }),
        description:
          description ||
          t('seo.description', {
            toolDescription: t(`tools.${slug}.description`),
          }),
        images: [`${SITE_URL}/og-image.jpg`],
      },
    }
  }

  // Fallback to translation-based metadata
  const toolName = t(`tools.${slug}.title`)
  const toolDescription = t(`tools.${slug}.description`)

  return {
    title: toolName,
    description: t('seo.description', {
      toolDescription: toolDescription,
    }),
    alternates: {
      canonical: `/${locale}/tools/${slug}`,
      languages: {
        en: `/en/tools/${slug}`,
        es: `/es/tools/${slug}`,
        fr: `/fr/tools/${slug}`,
        pt: `/pt/tools/${slug}`,
        zh: `/zh/tools/${slug}`,
        de: `/de/tools/${slug}`,
        ja: `/ja/tools/${slug}`,
        ru: `/ru/tools/${slug}`,
        it: `/it/tools/${slug}`,
      },
    },
    openGraph: {
      type: 'website',
      title: t('seo.title', {
        toolName: toolName,
        appName: APP_NAME,
      }),
      description: t('seo.description', {
        toolDescription: toolDescription,
      }),
      url: `${SITE_URL}/${locale}/tools/${slug}`,
      siteName: APP_NAME,
      images: [
        {
          url: `${SITE_URL}/og-image.jpg`,
          width: 1200,
          height: 630,
          alt: toolName,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('seo.title', {
        toolName: toolName,
        appName: APP_NAME,
      }),
      description: t('seo.description', {
        toolDescription: toolDescription,
      }),
      images: [`${SITE_URL}/og-image.jpg`],
    },
  }
}

export default async function ToolPage({
  params,
}: {
  params: Promise<{ slug: ToolSlugs; locale: string }>
}) {
  const { slug, locale } = await params

  // Enable static rendering
  setRequestLocale(locale)

  // Get translations
  const t = await getTranslations({ locale })

  // Find the tool by slug
  const tool = TOOLS.find((tool) => tool.slug === slug)

  // If tool not found, return 404
  if (!tool) {
    notFound()
  }

  // Get markdown content if available
  const markdown = await getToolMarkdown(slug, locale)

  // Determine input and output formats and FFmpeg command based on the tool slug
  let inputFormat = 'mp4'
  let outputFormat = 'mp3'
  let ffmpegCommand: string[] = []
  let outputMimeType = 'audio/mpeg'
  let isScreenRecorder = false

  switch (slug) {
    case 'screen-recorder':
      isScreenRecorder = true
      break
    case 'mp4-to-mp3':
      inputFormat = 'mp4'
      outputFormat = 'mp3'
      ffmpegCommand = [
        '-i',
        'input.mp4',
        '-vn',
        '-acodec',
        'libmp3lame',
        '-q:a',
        '2',
        'output.mp3',
      ]
      outputMimeType = 'audio/mpeg'
      break
    case 'video-to-audio':
      inputFormat = 'mp4,webm,mov,avi'
      outputFormat = 'mp3'
      ffmpegCommand = [
        '-i',
        'input.mp4',
        '-vn',
        '-acodec',
        'libmp3lame',
        '-q:a',
        '2',
        'output.mp3',
      ]
      outputMimeType = 'audio/mpeg'
      break
    case 'mp4-to-webm':
      inputFormat = 'mp4'
      outputFormat = 'webm'
      ffmpegCommand = [
        '-i',
        'input.mp4',
        '-c:v',
        'libvpx-vp9',
        '-crf',
        '30',
        '-b:v',
        '0',
        '-b:a',
        '128k',
        '-c:a',
        'libopus',
        'output.webm',
      ]
      outputMimeType = 'video/webm'
      break
    case 'webm-to-mp4':
      inputFormat = 'webm'
      outputFormat = 'mp4'
      ffmpegCommand = [
        '-i',
        'input.webm',
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-preset',
        'medium',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        'output.mp4',
      ]
      outputMimeType = 'video/mp4'
      break
    case 'mov-to-mp4':
      inputFormat = 'mov'
      outputFormat = 'mp4'
      ffmpegCommand = [
        '-i',
        'input.mov',
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-preset',
        'medium',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        'output.mp4',
      ]
      outputMimeType = 'video/mp4'
      break
    case 'avi-to-mp4':
      inputFormat = 'avi'
      outputFormat = 'mp4'
      ffmpegCommand = [
        '-i',
        'input.avi',
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-preset',
        'medium',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        'output.mp4',
      ]
      outputMimeType = 'video/mp4'
      break
    case 'mp4-to-gif':
      inputFormat = 'mp4'
      outputFormat = 'gif'
      ffmpegCommand = [
        '-i',
        'input.mp4',
        '-vf',
        'fps=10,scale=320:-1:flags=lanczos,split[s0][s1];[s0]palettegen=max_colors=128:stats_mode=diff[p];[s1][p]paletteuse=dither=bayer:bayer_scale=5:diff_mode=rectangle',
        '-loop',
        '0',
        'output.gif',
      ]
      outputMimeType = 'image/gif'
      break
    case 'gif-to-mp4':
      inputFormat = 'gif'
      outputFormat = 'mp4'
      ffmpegCommand = [
        '-i',
        'input.gif',
        '-movflags',
        'faststart',
        '-pix_fmt',
        'yuv420p',
        '-vf',
        'scale=trunc(iw/2)*2:trunc(ih/2)*2',
        '-c:v',
        'libx264',
        '-crf',
        '23',
        '-preset',
        'medium',
        'output.mp4',
      ]
      outputMimeType = 'video/mp4'
      break
    case 'mp3-to-wav':
      inputFormat = 'mp3'
      outputFormat = 'wav'
      ffmpegCommand = ['-i', 'input.mp3', '-acodec', 'pcm_s16le', 'output.wav']
      outputMimeType = 'audio/wav'
      break
    case 'wav-to-mp3':
      inputFormat = 'wav'
      outputFormat = 'mp3'
      ffmpegCommand = [
        '-i',
        'input.wav',
        '-acodec',
        'libmp3lame',
        '-q:a',
        '2',
        'output.mp3',
      ]
      outputMimeType = 'audio/mpeg'
      break
    default:
      notFound()
  }

  return (
    <div className="container mx-auto">
      {/* Breadcrumbs */}
      <nav className="text-muted-foreground mb-6 flex items-center text-sm">
        <Link href={`/`} className="hover:text-foreground">
          {t('nav.home')}
        </Link>
        <ChevronRight className="mx-2 h-4 w-4" />
        <Link href={`/tools`} className="hover:text-foreground">
          {t('nav.tools')}
        </Link>
        <ChevronRight className="mx-2 h-4 w-4" />
        <span className="text-foreground font-medium">
          {t(`tools.${slug}.title`)}
        </span>
      </nav>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-3 lg:grid-cols-4">
        {/* Main Content */}
        <div className="md:col-span-2 lg:col-span-3">
          <div className="mb-8">
            <h1 className="mb-2 text-3xl font-bold tracking-tight">
              {markdown?.frontmatter?.title || t(`tools.${slug}.title`)}
            </h1>
            <p className="text-muted-foreground">
              {markdown?.frontmatter?.description ||
                t(`tools.${slug}.description`)}
            </p>
          </div>

          <EnhancedToolSchema
            slug={slug}
            rating={4.8}
            ratingCount={35}
            datePublished="2023-01-01"
            dateModified={new Date().toISOString().split('T')[0]}
          />

          {/* Conversion Tool */}
          <div className="mb-12">
            <NoSSRWrapper>
              {isScreenRecorder ? (
                <ScreenRecorder toolSlug={slug} />
              ) : (
                <FFmpegConverter
                  toolSlug={slug}
                  inputFormat={inputFormat}
                  outputFormat={outputFormat}
                  ffmpegCommand={ffmpegCommand}
                  outputMimeType={outputMimeType}
                />
              )}
            </NoSSRWrapper>
          </div>

          {/* Markdown Content */}
          {markdown?.content && (
            <div className="mt-12 border-t pt-8">
              <h2 className="mb-6 text-2xl font-bold">About This Tool</h2>
              <MarkdownContent content={markdown.content} />
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="md:col-span-1">
          <RelatedToolsSidebar currentToolSlug={slug} maxTools={8} />
        </div>
      </div>
    </div>
  )
}
