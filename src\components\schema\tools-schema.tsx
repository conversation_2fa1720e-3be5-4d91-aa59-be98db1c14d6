'use client'

import { useEffect } from 'react'
import { useTranslations, useLocale } from 'next-intl'
import { APP_NAME, SITE_URL } from '@/lib/constants'

export function ToolsSchema() {
  const t = useTranslations()
  const locale = useLocale()

  useEffect(() => {
    // Breadcrumb Schema
    const breadcrumbScript = document.createElement('script')
    breadcrumbScript.type = 'application/ld+json'
    breadcrumbScript.id = 'breadcrumb-schema'

    const breadcrumbSchema = {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: t('nav.home'),
          item: `${SITE_URL}/${locale}`,
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: t('nav.tools'),
          item: `${SITE_URL}/${locale}/tools`,
        },
      ],
    }

    breadcrumbScript.innerHTML = JSON.stringify(breadcrumbSchema)
    document.head.appendChild(breadcrumbScript)

    // CollectionPage Schema
    const collectionScript = document.createElement('script')
    collectionScript.type = 'application/ld+json'
    collectionScript.id = 'collection-schema'

    const collectionSchema = {
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      name: `${t('nav.tools')} | ${APP_NAME}`,
      description: t('app.description'),
      url: `${SITE_URL}/${locale}/tools`,
      isPartOf: {
        '@type': 'WebSite',
        name: APP_NAME,
        url: `${SITE_URL}/${locale}`,
      },
    }

    collectionScript.innerHTML = JSON.stringify(collectionSchema)
    document.head.appendChild(collectionScript)

    return () => {
      const existingBreadcrumbScript =
        document.getElementById('breadcrumb-schema')
      if (existingBreadcrumbScript) {
        existingBreadcrumbScript.remove()
      }

      const existingCollectionScript =
        document.getElementById('collection-schema')
      if (existingCollectionScript) {
        existingCollectionScript.remove()
      }
    }
  }, [t, locale])

  return null
}
