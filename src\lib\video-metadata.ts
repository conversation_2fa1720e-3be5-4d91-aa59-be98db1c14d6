// Function to extract video metadata using HTML5 video element
export async function extractVideoMetadata(file: File): Promise<{
  width: number
  height: number
  duration: number
  fps?: number
}> {
  return new Promise((resolve, reject) => {
    // Create a URL for the video file
    const url = URL.createObjectURL(file)

    // Create a video element to load the file
    const video = document.createElement('video')

    // Set up event listeners
    video.onloadedmetadata = () => {
      // Get basic metadata
      const width = video.videoWidth
      const height = video.videoHeight
      const duration = video.duration

      // Clean up
      URL.revokeObjectURL(url)

      // Resolve with the metadata
      resolve({
        width,
        height,
        duration,
        // Note: FPS cannot be reliably determined from HTML5 video API alone
        // We'll use a default value or estimate it later if needed
      })
    }

    video.onerror = () => {
      // Clean up
      URL.revokeObjectURL(url)
      reject(new Error('Failed to load video metadata'))
    }

    // Load the video
    video.src = url
    video.load()
  })
}

// Function to generate resolution options based on original video size
export function generateResolutionOptions(
  originalWidth: number,
  originalHeight: number
): { label: string; width: number; height: number }[] {
  // Common resolution heights
  const standardHeights = [2160, 1440, 1080, 720, 480, 360, 240]

  // Calculate aspect ratio
  const aspectRatio = originalWidth / originalHeight

  // Generate options that maintain aspect ratio and are smaller than original
  return standardHeights
    .filter((height) => height <= originalHeight)
    .map((height) => {
      const width = Math.round(height * aspectRatio)
      return {
        label: `${width}x${height}`,
        width,
        height,
      }
    })
}

// Function to generate quality options (CRF values for H.264/H.265)
export function generateQualityOptions(): { label: string; value: number }[] {
  return [
    { label: 'Lossless (CRF 0)', value: 0 },
    { label: 'Visually Lossless (CRF 10)', value: 10 },
    { label: 'Highest (CRF 15)', value: 15 },
    { label: 'Higher (CRF 18)', value: 18 },
    { label: 'High (CRF 20)', value: 20 },
    { label: 'Good (CRF 23)', value: 23 },
    { label: 'Medium (CRF 26)', value: 26 },
    { label: 'Low (CRF 29)', value: 29 },
    { label: 'Lower (CRF 32)', value: 32 },
    { label: 'Lowest (CRF 35)', value: 35 },
    { label: 'Very Low (CRF 40)', value: 40 },
    { label: 'Minimum (CRF 51)', value: 51 },
  ]
}

// Function to generate FPS options
export function generateFpsOptions(): { label: string; value: number }[] {
  return [
    { label: '60 FPS', value: 60 },
    { label: '30 FPS', value: 30 },
    { label: '25 FPS', value: 25 },
    { label: '24 FPS', value: 24 },
    { label: '20 FPS', value: 20 },
    { label: '15 FPS', value: 15 },
  ]
}

// Function to generate audio bitrate options
export function generateAudioBitrateOptions(): {
  label: string
  value: string
}[] {
  return [
    { label: '32 kbps', value: '32k' },
    { label: '64 kbps', value: '64k' },
    { label: '96 kbps', value: '96k' },
    { label: '128 kbps', value: '128k' },
    { label: '192 kbps', value: '192k' },
    { label: '256 kbps', value: '256k' },
    { label: '320 kbps', value: '320k' },
    { label: '384 kbps', value: '384k' },
    { label: '448 kbps', value: '448k' },
    { label: '512 kbps', value: '512k' },
    { label: '576 kbps', value: '576k' },
    { label: '640 kbps', value: '640k' },
  ]
}

// Function to generate aspect ratio options
export function generateAspectRatioOptions(): {
  label: string
  value: string
}[] {
  return [
    { label: 'Keep Original', value: 'original' },
    { label: '16:9', value: '16:9' },
    { label: '4:3', value: '4:3' },
    { label: '1:1 (Square)', value: '1:1' },
    { label: '9:16 (Vertical)', value: '9:16' },
    { label: '21:9 (Ultrawide)', value: '21:9' },
    { label: '2.35:1 (Cinemascope)', value: '2.35:1' },
    { label: '3:2', value: '3:2' },
    { label: '5:4', value: '5:4' },
  ]
}

// Function to generate scaling methods
export function generateScalingMethodOptions(): {
  label: string
  value: string
}[] {
  return [
    { label: 'Fit (Preserve Aspect Ratio)', value: 'fit' },
    { label: 'Fill (Crop to Fill)', value: 'fill' },
    { label: 'Stretch (Ignore Aspect Ratio)', value: 'stretch' },
  ]
}

// Function to generate keyframe interval options
export function generateKeyframeIntervalOptions(): {
  label: string
  value: number
}[] {
  return [
    { label: 'Auto', value: 0 },
    { label: '1 second', value: 1 },
    { label: '2 seconds', value: 2 },
    { label: '5 seconds', value: 5 },
    { label: '10 seconds', value: 10 },
    { label: '30 seconds', value: 30 },
    { label: '60 seconds', value: 60 },
    { label: '120 seconds', value: 120 },
  ]
}

// Function to generate video codec options
export function generateVideoCodecOptions(): {
  label: string
  value: string
}[] {
  return [
    { label: 'Auto (Based on Format)', value: 'auto' },
    { label: 'H.264 (AVC)', value: 'libx264' },
    { label: 'H.265 (HEVC)', value: 'libx265' },
    { label: 'VP9', value: 'libvpx-vp9' },
    { label: 'VP8', value: 'libvpx' },
    { label: 'AV1', value: 'libaom-av1' },
    { label: 'MPEG-4', value: 'mpeg4' },
    { label: 'MPEG-2', value: 'mpeg2video' },
    { label: 'ProRes', value: 'prores' },
    { label: 'DNxHD', value: 'dnxhd' },
  ]
}

// Function to generate audio codec options
export function generateAudioCodecOptions(): {
  label: string
  value: string
}[] {
  return [
    { label: 'Auto (Based on Format)', value: 'auto' },
    { label: 'AAC', value: 'aac' },
    { label: 'MP3', value: 'libmp3lame' },
    { label: 'Opus', value: 'libopus' },
    { label: 'Vorbis', value: 'libvorbis' },
    { label: 'FLAC', value: 'flac' },
    { label: 'PCM (Uncompressed)', value: 'pcm_s16le' },
    { label: 'AC3', value: 'ac3' },
    { label: 'EAC3', value: 'eac3' },
  ]
}

// Function to generate audio channel options
export function generateAudioChannelOptions(): {
  label: string
  value: number
}[] {
  return [
    { label: 'Auto (Keep Original)', value: 0 },
    { label: 'Mono (1 channel)', value: 1 },
    { label: 'Stereo (2 channels)', value: 2 },
    { label: '5.1 Surround (6 channels)', value: 6 },
    { label: '7.1 Surround (8 channels)', value: 8 },
  ]
}

// Function to generate audio sample rate options
export function generateAudioSampleRateOptions(): {
  label: string
  value: number
}[] {
  return [
    { label: 'Auto (Keep Original)', value: 0 },
    { label: '8,000 Hz', value: 8000 },
    { label: '11,025 Hz', value: 11025 },
    { label: '16,000 Hz', value: 16000 },
    { label: '22,050 Hz', value: 22050 },
    { label: '32,000 Hz', value: 32000 },
    { label: '44,100 Hz (CD Quality)', value: 44100 },
    { label: '48,000 Hz (DVD Quality)', value: 48000 },
    { label: '96,000 Hz (Studio Quality)', value: 96000 },
    { label: '192,000 Hz (Hi-Res)', value: 192000 },
  ]
}

// Function to generate audio volume options
export function generateAudioVolumeOptions(): {
  label: string
  value: number
}[] {
  return [
    { label: 'Original Volume', value: 1.0 },
    { label: 'Silent', value: 0.0 },
    { label: '25% Volume', value: 0.25 },
    { label: '50% Volume', value: 0.5 },
    { label: '75% Volume', value: 0.75 },
    { label: '100% Volume', value: 1.0 },
    { label: '125% Volume', value: 1.25 },
    { label: '150% Volume', value: 1.5 },
    { label: '200% Volume', value: 2.0 },
    { label: '300% Volume', value: 3.0 },
  ]
}

// Function to modify FFmpeg command with custom settings
export function applySettingsToFFmpegCommand(
  command: string[],
  settings: {
    resolution?: { width: number; height: number }
    quality?: number
    fps?: number
    audioBitrate?: string
    outputFormat: string
    optimizeMemory?: boolean
    aspectRatio?: string
    scalingMethod?: string
    keyframeInterval?: number
    videoCodec?: string
    audioCodec?: string
    audioChannels?: number
    audioSampleRate?: number
    audioVolume?: number
    customResolution?: { width: number; height: number }
  }
): string[] {
  const {
    resolution,
    quality,
    fps,
    audioBitrate,
    outputFormat,
    optimizeMemory = true,
    aspectRatio,
    scalingMethod,
    keyframeInterval,
    videoCodec,
    audioCodec,
    audioChannels,
    audioSampleRate,
    audioVolume,
    customResolution,
  } = settings

  // Create a copy of the command to modify
  const newCommand = [...command]

  // Find the output file index (it's usually the last item)
  const outputIndex = newCommand.length - 1

  // Add memory optimization flags if requested
  if (optimizeMemory) {
    // Check if we already have memory optimization flags
    if (!newCommand.includes('-threads')) {
      // Add thread limitation to reduce memory usage
      newCommand.splice(outputIndex, 0, '-threads', '1')
    }

    // Add memory optimization flags for large files
    if (!newCommand.includes('-max_muxing_queue_size')) {
      newCommand.splice(outputIndex, 0, '-max_muxing_queue_size', '1024')
    }

    // Add additional memory optimization flags

    // Reduce buffer size to minimize memory usage
    if (!newCommand.includes('-bufsize')) {
      newCommand.splice(outputIndex, 0, '-bufsize', '8192k')
    }

    // Use simpler encoding options
    if (
      !newCommand.includes('-preset') &&
      (outputFormat === 'mp4' ||
        outputFormat === 'mkv' ||
        outputFormat === 'webm')
    ) {
      // Use the fastest preset to reduce memory usage
      newCommand.splice(outputIndex, 0, '-preset', 'ultrafast')
    }

    // Disable complex filters that use a lot of memory
    if (!newCommand.includes('-sws_flags')) {
      newCommand.splice(outputIndex, 0, '-sws_flags', 'bilinear')
    }

    // Disable multi-threading in libx264 (separate from ffmpeg threads)
    if (!newCommand.includes('-x264opts')) {
      newCommand.splice(outputIndex, 0, '-x264opts', 'threads=1')
    }
  }

  // Add resolution if specified
  if (resolution) {
    // Check if there's already a scale filter
    const vfIndex = newCommand.indexOf('-vf')

    if (vfIndex !== -1 && vfIndex + 1 < outputIndex) {
      // Modify existing filter
      const filterValue = newCommand[vfIndex + 1]
      newCommand[vfIndex + 1] = filterValue.includes('scale=')
        ? filterValue.replace(
            /scale=[^,]+/,
            `scale=${resolution.width}:${resolution.height}`
          )
        : `scale=${resolution.width}:${resolution.height},${filterValue}`
    } else {
      // Add new scale filter before the output file
      newCommand.splice(
        outputIndex,
        0,
        '-vf',
        `scale=${resolution.width}:${resolution.height}`
      )
    }
  }

  // Add quality (CRF) if specified
  if (quality !== undefined) {
    const crfIndex = newCommand.indexOf('-crf')

    if (crfIndex !== -1 && crfIndex + 1 < outputIndex) {
      // Replace existing CRF value
      newCommand[crfIndex + 1] = quality.toString()
    } else {
      // Add new CRF parameter before the output file
      newCommand.splice(outputIndex, 0, '-crf', quality.toString())
    }
  }

  // Add FPS if specified
  if (fps !== undefined) {
    const rIndex = newCommand.indexOf('-r')

    if (rIndex !== -1 && rIndex + 1 < outputIndex) {
      // Replace existing FPS value
      newCommand[rIndex + 1] = fps.toString()
    } else {
      // Add new FPS parameter before the output file
      newCommand.splice(outputIndex, 0, '-r', fps.toString())
    }
  }

  // Add audio bitrate if specified
  if (audioBitrate) {
    const baIndex = newCommand.indexOf('-b:a')

    if (baIndex !== -1 && baIndex + 1 < outputIndex) {
      // Replace existing audio bitrate value
      newCommand[baIndex + 1] = audioBitrate
    } else {
      // Add new audio bitrate parameter before the output file
      newCommand.splice(outputIndex, 0, '-b:a', audioBitrate)
    }
  }

  // Add video codec if specified and not auto
  if (videoCodec && videoCodec !== 'auto') {
    const vcIndex = newCommand.indexOf('-c:v')

    if (vcIndex !== -1 && vcIndex + 1 < outputIndex) {
      // Replace existing video codec
      newCommand[vcIndex + 1] = videoCodec
    } else {
      // Add new video codec parameter
      newCommand.splice(outputIndex, 0, '-c:v', videoCodec)
    }
  }

  // Add audio codec if specified and not auto
  if (audioCodec && audioCodec !== 'auto') {
    const acIndex = newCommand.indexOf('-c:a')

    if (acIndex !== -1 && acIndex + 1 < outputIndex) {
      // Replace existing audio codec
      newCommand[acIndex + 1] = audioCodec
    } else {
      // Add new audio codec parameter
      newCommand.splice(outputIndex, 0, '-c:a', audioCodec)
    }
  }

  // Add keyframe interval if specified and not auto
  if (keyframeInterval && keyframeInterval > 0) {
    const gIndex = newCommand.indexOf('-g')

    if (gIndex !== -1 && gIndex + 1 < outputIndex) {
      // Replace existing keyframe interval
      newCommand[gIndex + 1] = keyframeInterval.toString()
    } else {
      // Add new keyframe interval parameter
      newCommand.splice(outputIndex, 0, '-g', keyframeInterval.toString())
    }
  }

  // Add audio channels if specified and not auto
  if (audioChannels && audioChannels > 0) {
    const acIndex = newCommand.indexOf('-ac')

    if (acIndex !== -1 && acIndex + 1 < outputIndex) {
      // Replace existing audio channels
      newCommand[acIndex + 1] = audioChannels.toString()
    } else {
      // Add new audio channels parameter
      newCommand.splice(outputIndex, 0, '-ac', audioChannels.toString())
    }
  }

  // Add audio sample rate if specified and not auto
  if (audioSampleRate && audioSampleRate > 0) {
    const arIndex = newCommand.indexOf('-ar')

    if (arIndex !== -1 && arIndex + 1 < outputIndex) {
      // Replace existing audio sample rate
      newCommand[arIndex + 1] = audioSampleRate.toString()
    } else {
      // Add new audio sample rate parameter
      newCommand.splice(outputIndex, 0, '-ar', audioSampleRate.toString())
    }
  }

  // Add audio volume if specified and not default (1.0)
  if (audioVolume !== undefined && audioVolume !== 1.0) {
    // Check if there's already an audio filter
    const afIndex = newCommand.indexOf('-af')

    if (afIndex !== -1 && afIndex + 1 < outputIndex) {
      // Modify existing audio filter
      const filterValue = newCommand[afIndex + 1]
      newCommand[afIndex + 1] = filterValue.includes('volume=')
        ? filterValue.replace(/volume=[^,]+/, `volume=${audioVolume}`)
        : `volume=${audioVolume},${filterValue}`
    } else {
      // Add new audio filter
      newCommand.splice(outputIndex, 0, '-af', `volume=${audioVolume}`)
    }
  }

  // Handle custom resolution and aspect ratio
  if (
    customResolution &&
    customResolution.width > 0 &&
    customResolution.height > 0
  ) {
    // Use custom resolution instead of preset resolution
    const vfIndex = newCommand.indexOf('-vf')

    if (vfIndex !== -1 && vfIndex + 1 < outputIndex) {
      // Modify existing filter
      const filterValue = newCommand[vfIndex + 1]
      newCommand[vfIndex + 1] = filterValue.includes('scale=')
        ? filterValue.replace(
            /scale=[^,]+/,
            `scale=${customResolution.width}:${customResolution.height}`
          )
        : `scale=${customResolution.width}:${customResolution.height},${filterValue}`
    } else {
      // Add new scale filter
      newCommand.splice(
        outputIndex,
        0,
        '-vf',
        `scale=${customResolution.width}:${customResolution.height}`
      )
    }
  } else if (aspectRatio && aspectRatio !== 'original' && resolution) {
    // Apply aspect ratio to resolution
    let targetWidth = resolution.width
    let targetHeight = resolution.height

    // Parse aspect ratio (format: "16:9", "4:3", etc.)
    if (aspectRatio.includes(':')) {
      const [widthRatio, heightRatio] = aspectRatio.split(':').map(Number)

      if (scalingMethod === 'fit') {
        // Fit: maintain aspect ratio, fit within the resolution bounds
        if (targetWidth / targetHeight > widthRatio / heightRatio) {
          // Width is the limiting factor
          targetWidth = Math.floor(targetHeight * (widthRatio / heightRatio))
        } else {
          // Height is the limiting factor
          targetHeight = Math.floor(targetWidth / (widthRatio / heightRatio))
        }
      } else if (scalingMethod === 'fill') {
        // Fill: maintain aspect ratio, fill the resolution bounds (may crop)
        if (targetWidth / targetHeight < widthRatio / heightRatio) {
          // Width is the limiting factor
          targetWidth = Math.floor(targetHeight * (widthRatio / heightRatio))
        } else {
          // Height is the limiting factor
          targetHeight = Math.floor(targetWidth / (widthRatio / heightRatio))
        }

        // Add crop filter to maintain exact resolution
        const vfIndex = newCommand.indexOf('-vf')

        if (vfIndex !== -1 && vfIndex + 1 < outputIndex) {
          // Modify existing filter
          const filterValue = newCommand[vfIndex + 1]
          const cropFilter = `crop=${resolution.width}:${resolution.height}:(iw-${resolution.width})/2:(ih-${resolution.height})/2`

          if (filterValue.includes('scale=')) {
            newCommand[vfIndex + 1] = filterValue.replace(
              /scale=[^,]+/,
              `scale=${targetWidth}:${targetHeight},${cropFilter}`
            )
          } else {
            newCommand[vfIndex + 1] =
              `scale=${targetWidth}:${targetHeight},${cropFilter},${filterValue}`
          }
        } else {
          // Add new scale and crop filter
          newCommand.splice(
            outputIndex,
            0,
            '-vf',
            `scale=${targetWidth}:${targetHeight},crop=${resolution.width}:${resolution.height}:(iw-${resolution.width})/2:(ih-${resolution.height})/2`
          )
        }

        // Skip the regular scale filter since we've already added it with crop
        return newCommand
      }
      // For 'stretch', we don't modify the target dimensions
    }

    // Apply the new dimensions
    const vfIndex = newCommand.indexOf('-vf')

    if (vfIndex !== -1 && vfIndex + 1 < outputIndex) {
      // Modify existing filter
      const filterValue = newCommand[vfIndex + 1]
      newCommand[vfIndex + 1] = filterValue.includes('scale=')
        ? filterValue.replace(
            /scale=[^,]+/,
            `scale=${targetWidth}:${targetHeight}`
          )
        : `scale=${targetWidth}:${targetHeight},${filterValue}`
    } else {
      // Add new scale filter
      newCommand.splice(
        outputIndex,
        0,
        '-vf',
        `scale=${targetWidth}:${targetHeight}`
      )
    }
  }

  return newCommand
}
