import { MetadataRoute } from 'next'
import { SITE_URL } from '@/lib/constants'

/**
 * Generate robots.txt file
 */
export default function robots(): MetadataRoute.Robots {
  return {
    rules: {
      userAgent: '*',
      allow: '/',
      disallow: [
        '/api/',
        '/trpc/',
        '/_next/',
        '/_vercel/',
        '/terms',
        '/privacy',
        '/about',
        '/dmca',
      ],
    },
    sitemap: `${SITE_URL}/sitemap.xml`,
    host: SITE_URL,
  }
}
