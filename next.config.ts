import createNextIntlPlugin from 'next-intl/plugin'
import type { NextConfig } from 'next'

const withNextIntl = createNextIntlPlugin({
  requestConfig: './src/i18n/request.ts',
  experimental: {
    createMessagesDeclaration: ['./src/messages/en.json'],
  },
})

const nextConfig: NextConfig = {
  reactStrictMode: true,
  crossOrigin: 'anonymous',
  // These headers are needed for SharedArrayBuffer to work (required by FFmpeg.wasm)
  headers: async () => {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'require-corp',
          },
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'same-origin',
          },
        ],
      },
    ]
  },
}

export default withNextIntl(nextConfig)
