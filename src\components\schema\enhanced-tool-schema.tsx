'use client'

import { useEffect } from 'react'
import { useTranslations, useLocale } from 'next-intl'
import { SITE_URL, APP_NAME } from '@/lib/constants'

// Helper function to parse slug for schema
function parseSlugForSchema(slug: string): {
  inputFormat?: string
  outputFormat?: string
} {
  // Check if it's a format-to-format pattern
  const formatToFormatMatch = slug.match(/^(\w+)-to-(\w+)$/)
  if (formatToFormatMatch) {
    return {
      inputFormat: formatToFormatMatch[1],
      outputFormat: formatToFormatMatch[2],
    }
  }

  // Check if it's a format-a-format pattern (Spanish)
  const formatAFormatMatch = slug.match(/^(\w+)-a-(\w+)$/)
  if (formatAFormatMatch) {
    return {
      inputFormat: formatAFormatMatch[1],
      outputFormat: formatAFormatMatch[2],
    }
  }

  // Check if it's a single format converter
  const singleFormatMatch = slug.match(/^(\w+)-converter$/)
  if (singleFormatMatch) {
    return {
      inputFormat: singleFormatMatch[1],
      outputFormat: singleFormatMatch[1],
    }
  }

  return {}
}

interface EnhancedToolSchemaProps {
  slug: string
  rating?: number
  ratingCount?: number
  datePublished?: string
  dateModified?: string
}

export function EnhancedToolSchema({
  slug,
  rating = 4.8,
  ratingCount = 35,
  datePublished = '2023-01-01',
  dateModified = new Date().toISOString().split('T')[0],
}: EnhancedToolSchemaProps) {
  const t = useTranslations()
  const locale = useLocale()

  useEffect(() => {
    // Generate title and description based on the slug
    let toolName, toolDescription, toolCategory, toolKeywords

    // For dynamic converter slugs, use the dynamicConverter translations
    const { inputFormat, outputFormat } = parseSlugForSchema(slug)
    if (inputFormat && outputFormat && inputFormat !== outputFormat) {
      toolName = `${inputFormat.toUpperCase()} to ${outputFormat.toUpperCase()} Converter`
      toolDescription = `Convert ${inputFormat.toUpperCase()} files to ${outputFormat.toUpperCase()} format online for free. No download required.`
      
      // Determine category based on formats
      const videoFormats = ['mp4', 'webm', 'mov', 'avi']
      const audioFormats = ['mp3', 'wav', 'aac', 'ogg']
      const imageFormats = ['jpg', 'png', 'gif', 'webp']
      
      if (videoFormats.includes(inputFormat) && videoFormats.includes(outputFormat)) {
        toolCategory = 'VideoConversionTool'
        toolKeywords = `${inputFormat} to ${outputFormat}, video converter, online video converter, free video converter`
      } else if (audioFormats.includes(inputFormat) && audioFormats.includes(outputFormat)) {
        toolCategory = 'AudioConversionTool'
        toolKeywords = `${inputFormat} to ${outputFormat}, audio converter, online audio converter, free audio converter`
      } else if (videoFormats.includes(inputFormat) && audioFormats.includes(outputFormat)) {
        toolCategory = 'AudioExtractionTool'
        toolKeywords = `${inputFormat} to ${outputFormat}, extract audio, video to audio, online converter`
      } else if (imageFormats.includes(inputFormat) || imageFormats.includes(outputFormat)) {
        toolCategory = 'ImageConversionTool'
        toolKeywords = `${inputFormat} to ${outputFormat}, image converter, online image converter, free image converter`
      } else {
        toolCategory = 'FileConversionTool'
        toolKeywords = `${inputFormat} to ${outputFormat}, file converter, online converter, free converter`
      }
    } else if (inputFormat) {
      toolName = `${inputFormat.toUpperCase()} Converter`
      toolDescription = `Convert ${inputFormat.toUpperCase()} files to other formats online for free. No download required.`
      toolCategory = 'FileConversionTool'
      toolKeywords = `${inputFormat} converter, online ${inputFormat} converter, free ${inputFormat} converter`
    } else {
      // Fallback
      toolName = slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      toolDescription = `Convert files online for free. No download required.`
      toolCategory = 'FileConversionTool'
      toolKeywords = `file converter, online converter, free converter`
    }

    const toolUrl = `${SITE_URL}/${locale}/tools/${slug}`

    // Tool Schema (SoftwareApplication)
    const toolSchema = {
      '@context': 'https://schema.org',
      '@type': 'SoftwareApplication',
      name: toolName,
      headline: toolName,
      alternativeHeadline: `Free Online ${toolName}`,
      description: toolDescription,
      applicationCategory: 'MultimediaApplication',
      applicationSubCategory: toolCategory,
      operatingSystem: 'Any',
      keywords: toolKeywords,
      datePublished: datePublished,
      dateModified: dateModified,
      author: {
        '@type': 'Organization',
        name: APP_NAME,
        url: SITE_URL
      },
      publisher: {
        '@type': 'Organization',
        name: APP_NAME,
        url: SITE_URL
      },
      offers: {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'USD',
        availability: 'https://schema.org/InStock',
      },
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue: rating.toString(),
        ratingCount: ratingCount.toString(),
        bestRating: '5',
        worstRating: '1',
      },
      url: toolUrl,
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': toolUrl
      }
    }

    // Create and append the tool schema
    const toolScript = document.createElement('script')
    toolScript.type = 'application/ld+json'
    toolScript.id = 'tool-schema'
    toolScript.innerHTML = JSON.stringify(toolSchema)
    document.head.appendChild(toolScript)

    // Breadcrumb Schema
    const breadcrumbSchema = {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: t('nav.home'),
          item: `${SITE_URL}/${locale}`,
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: t('nav.tools'),
          item: `${SITE_URL}/${locale}/tools`,
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: toolName,
          item: toolUrl,
        },
      ],
    }

    // Create and append the breadcrumb schema
    const breadcrumbScript = document.createElement('script')
    breadcrumbScript.type = 'application/ld+json'
    breadcrumbScript.id = 'breadcrumb-schema'
    breadcrumbScript.innerHTML = JSON.stringify(breadcrumbSchema)
    document.head.appendChild(breadcrumbScript)

    return () => {
      // Clean up scripts when component unmounts
      const existingToolScript = document.getElementById('tool-schema')
      if (existingToolScript) {
        existingToolScript.remove()
      }

      const existingBreadcrumbScript = document.getElementById('breadcrumb-schema')
      if (existingBreadcrumbScript) {
        existingBreadcrumbScript.remove()
      }
    }
  }, [slug, t, locale, rating, ratingCount, datePublished, dateModified])

  return null
}
