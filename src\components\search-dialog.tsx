'use client'

import { useState, useEffect, useCallback } from 'react'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import { TOOLS, ToolSlugs } from '@/lib/constants'
import { Search, X } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'

interface SearchResult {
  tool: (typeof TOOLS)[number]
  score: number
}

interface SearchDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function SearchDialog({ open, onOpenChange }: SearchDialogProps) {
  const t = useTranslations()
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])

  // Calculate search score for a tool based on multiple keywords
  const calculateSearchScore = useCallback(
    (tool: (typeof TOOLS)[number], query: string) => {
      const title = t(`tools.${tool.slug}.title`).toLowerCase()
      const description = t(`tools.${tool.slug}.description`).toLowerCase()
      const slug = tool.slug.toLowerCase()

      // Split query into keywords
      const keywords = query.toLowerCase().split(/\s+/).filter(Boolean)

      if (keywords.length === 0) return 0

      let totalScore = 0

      for (const keyword of keywords) {
        let keywordScore = 0

        // Exact match in title (highest priority)
        if (title === keyword) {
          keywordScore += 100
        }
        // Title starts with keyword
        else if (title.startsWith(keyword)) {
          keywordScore += 80
        }
        // Title contains keyword
        else if (title.includes(keyword)) {
          keywordScore += 60
        }

        // Slug exact match
        if (slug === keyword) {
          keywordScore += 50
        }
        // Slug contains keyword
        else if (slug.includes(keyword)) {
          keywordScore += 40
        }

        // Description contains keyword
        if (description.includes(keyword)) {
          keywordScore += 20
        }

        totalScore += keywordScore
      }

      // Normalize by number of keywords for fair comparison
      return totalScore / keywords.length
    },
    [t]
  )

  // Filter tools based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setSearchResults([])
      return
    }

    const results: SearchResult[] = TOOLS.map((tool) => ({
      tool,
      score: calculateSearchScore(tool, searchQuery),
    }))
      .filter((result) => result.score > 0)
      .sort((a, b) => b.score - a.score)

    setSearchResults(results)
  }, [searchQuery, calculateSearchScore])

  // Handle navigation to a tool
  const handleToolSelect = (slug: ToolSlugs) => {
    router.push(`/tools/${slug}`)
    onOpenChange(false)
    setSearchQuery('')
  }

  // Focus input when dialog opens
  useEffect(() => {
    if (open) {
      const timer = setTimeout(() => {
        const input = document.getElementById('search-dialog-input')
        if (input) {
          input.focus()
        }
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [open])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-h-screen gap-0 overflow-hidden p-0 sm:max-w-[500px] md:max-h-[85vh]"
        closeButtonClassName="hidden"
      >
        <div className="flex items-center border-b p-4">
          <Search className="text-muted-foreground mr-3 h-5 w-5 flex-shrink-0" />
          <Input
            id="search-dialog-input"
            placeholder={t('search.placeholder')}
            className="bg-background border-input flex-1 rounded-md border px-2 py-1.5 text-base"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Button
            variant="ghost"
            size="icon"
            className="ml-2 flex-shrink-0"
            onClick={() => onOpenChange(false)}
          >
            <X className="h-5 w-5" />
            <span className="sr-only">Close</span>
          </Button>
        </div>

        <div className="max-h-[calc(85vh-80px)] overflow-y-auto md:max-h-[500px]">
          {searchResults.length > 0 ? (
            <div className="py-2 pb-8">
              {searchResults.map(({ tool }) => (
                <button
                  key={tool.slug}
                  className="hover:bg-accent flex w-full items-center px-4 py-3 text-left"
                  onClick={() => handleToolSelect(tool.slug)}
                >
                  <div>
                    <div className="font-medium">
                      {t(`tools.${tool.slug}.title`)}
                    </div>
                    <div className="text-muted-foreground line-clamp-1 text-sm">
                      {t(`tools.${tool.slug}.description`)}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          ) : searchQuery ? (
            <div className="text-muted-foreground p-4 text-center">
              {t('search.noResults')}
            </div>
          ) : (
            <div className="p-4 pb-8">
              <h3 className="mb-3 font-medium">{t('search.popularTools')}</h3>
              <div className="grid grid-cols-2 gap-3">
                {TOOLS.slice(0, 6).map((tool) => (
                  <button
                    key={tool.slug}
                    className="hover:bg-accent rounded-md p-3 text-left text-sm"
                    onClick={() => handleToolSelect(tool.slug)}
                  >
                    {t(`tools.${tool.slug}.title`)}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
