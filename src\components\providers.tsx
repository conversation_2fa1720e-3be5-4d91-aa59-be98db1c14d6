'use client'
import { PropsWithChildren } from 'react'
import { ThemeProvider as NextThemesProvider } from 'next-themes'

export function Providers({ children }: PropsWithChildren) {
  return (
    <NextThemesProvider
      attribute="class"
      enableSystem={true}
      defaultTheme="light"
      enableColorScheme={true}
      disableTransitionOnChange
    >
      {children}
    </NextThemesProvider>
  )
}
