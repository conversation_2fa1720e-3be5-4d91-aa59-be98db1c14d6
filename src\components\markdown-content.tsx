/* eslint-disable */
'use client'

import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'

interface MarkdownContentProps {
  content: string
}

export function MarkdownContent({ content }: MarkdownContentProps) {
  return (
    <div className="prose dark:prose-invert prose-headings:scroll-mt-20 prose-img:rounded-lg prose-h1:text-3xl prose-h1:font-bold prose-h1:mb-6 prose-h1:mt-8 prose-h2:text-2xl prose-h2:font-semibold prose-h2:mb-4 prose-h2:mt-8 prose-h2:border-b prose-h2:pb-2 prose-h3:text-xl prose-h3:font-medium prose-h3:mb-3 prose-h3:mt-6 prose-p:my-4 prose-p:leading-relaxed prose-ul:my-4 prose-ul:list-disc prose-ul:pl-6 prose-ol:my-4 prose-ol:list-decimal prose-ol:pl-6 prose-li:my-2 prose-li:pl-1 prose-strong:font-semibold prose-blockquote:my-4 prose-blockquote:border-l-4 prose-blockquote:border-gray-300 dark:prose-blockquote:border-gray-700 prose-blockquote:pl-4 prose-blockquote:italic prose-table:my-6 max-w-none">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          h1: ({ node, ...props }) => (
            <h1 className="mt-8 mb-6 text-3xl font-bold" {...props} />
          ),
          h2: ({ node, ...props }) => (
            <h2
              className="mt-8 mb-4 border-b pb-2 text-2xl font-semibold"
              {...props}
            />
          ),
          h3: ({ node, ...props }) => (
            <h3 className="mt-6 mb-3 text-xl font-medium" {...props} />
          ),
          p: ({ node, ...props }) => (
            <p className="my-4 leading-relaxed" {...props} />
          ),
          ul: ({ node, ...props }) => (
            <ul className="my-4 list-disc pl-6" {...props} />
          ),
          ol: ({ node, ...props }) => (
            <ol className="my-4 list-decimal pl-6" {...props} />
          ),
          li: ({ node, ...props }) => <li className="my-2 pl-1" {...props} />,
          strong: ({ node, ...props }) => (
            <strong className="font-semibold" {...props} />
          ),
          blockquote: ({ node, ...props }) => (
            <blockquote
              className="my-4 border-l-4 border-gray-300 pl-4 italic dark:border-gray-700"
              {...props}
            />
          ),
          code: ({ className, children, ...props }: any) => {
            return (
              <code
                className="rounded bg-gray-100 px-1 py-0.5 text-sm dark:bg-gray-800"
                {...props}
              >
                {children}
              </code>
            )
          },
          pre: ({ node, ...props }) => (
            <pre
              className="my-4 overflow-x-auto rounded-md bg-gray-100 p-4 dark:bg-gray-800"
              {...props}
            />
          ),
          table: ({ node, ...props }) => (
            <div className="markdown-table-wrapper">
              <table className="markdown-table" {...props} />
            </div>
          ),
          thead: ({ node, ...props }) => <thead {...props} />,
          th: ({ node, ...props }) => <th {...props} />,
          td: ({ node, ...props }) => <td {...props} />,
          tr: ({ node, ...props }) => <tr {...props} />,
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  )
}
