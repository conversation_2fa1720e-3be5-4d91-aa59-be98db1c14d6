'use client'

import React from 'react'
import { useTranslations } from 'next-intl'
import { Link } from '@/i18n/navigation'
import { ChevronDown, ChevronRight } from 'lucide-react'

// Define types for our navigation items
export type SubItem = {
  name: string
  href: string
  description?: string
}

export type NavCategory = {
  name: string
  href: string
  description?: string
  items: SubItem[]
}
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu'
import { SheetClose } from '@/components/ui/sheet'
import { TOOL_CATEGORIES, TOOLS } from '@/lib/constants'

// Desktop navigation items
export function DesktopNavigationItems() {
  const t = useTranslations()

  return (
    <NavigationMenu className="relative">
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuLink asChild className={navigationMenuTriggerStyle()}>
            <Link href="/">{t('nav.home')}</Link>
          </NavigationMenuLink>
        </NavigationMenuItem>

        <NavigationMenuItem>
          <NavigationMenuLink asChild className={navigationMenuTriggerStyle()}>
            <Link href="/tools">{t('nav.tools')}</Link>
          </NavigationMenuLink>
        </NavigationMenuItem>

        <NavigationMenuItem>
          <NavigationMenuTrigger>Our Services</NavigationMenuTrigger>
          <NavigationMenuContent className="navigation-menu-content">
            <div className="p-6" style={{ width: 'auto', minWidth: '600px' }}>
              <div
                className="grid grid-cols-2 gap-8"
                style={{ minWidth: '180px' }}
              >
                {TOOL_CATEGORIES.map((category) => {
                  // Get tools for this category
                  const categoryTools = TOOLS.filter(
                    (tool) => tool.category === category.slug
                  )

                  return (
                    <div
                      key={category.slug}
                      className="min-w-[200px] space-y-3"
                    >
                      <Link
                        href={`/tools#${category.slug}`}
                        className="text-foreground hover:text-primary mb-2 block border-b pb-2 text-lg font-medium whitespace-nowrap"
                      >
                        {category.name}
                      </Link>

                      <ul className="space-y-1">
                        {categoryTools.map((tool) => (
                          <li key={tool.slug}>
                            <NavigationMenuLink asChild>
                              <Link
                                href={`/tools/${tool.slug}`}
                                className="hover:text-primary block py-1 text-sm hover:underline"
                              >
                                {tool.name}
                              </Link>
                            </NavigationMenuLink>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )
                })}
              </div>
            </div>
          </NavigationMenuContent>
        </NavigationMenuItem>

        <NavigationMenuItem>
          <NavigationMenuTrigger>Features</NavigationMenuTrigger>
          <NavigationMenuContent className="navigation-menu-content">
            <div className="p-6" style={{ width: 'auto', minWidth: '600px' }}>
              <div
                className="grid grid-cols-2 gap-8"
                style={{ minWidth: '180px' }}
              >
                <div className="min-w-[200px] space-y-3">
                  <Link
                    href="/features"
                    className="text-foreground hover:text-primary mb-2 block border-b pb-2 text-lg font-medium whitespace-nowrap"
                  >
                    Main Features
                  </Link>
                  <ul className="space-y-1">
                    <li>
                      <NavigationMenuLink asChild>
                        <Link
                          href="/features/feature-1"
                          className="hover:text-primary block py-1 text-sm hover:underline"
                        >
                          Feature 1
                        </Link>
                      </NavigationMenuLink>
                    </li>
                    <li>
                      <NavigationMenuLink asChild>
                        <Link
                          href="/features/feature-2"
                          className="hover:text-primary block py-1 text-sm hover:underline"
                        >
                          Feature 2
                        </Link>
                      </NavigationMenuLink>
                    </li>
                  </ul>
                </div>

                <div className="min-w-[200px] space-y-3">
                  <Link
                    href="/features/advanced"
                    className="text-foreground hover:text-primary mb-2 block border-b pb-2 text-lg font-medium whitespace-nowrap"
                  >
                    Advanced Features
                  </Link>
                  <ul className="space-y-1">
                    <li>
                      <NavigationMenuLink asChild>
                        <Link
                          href="/features/advanced/feature-3"
                          className="hover:text-primary block py-1 text-sm hover:underline"
                        >
                          Feature 3
                        </Link>
                      </NavigationMenuLink>
                    </li>
                    <li>
                      <NavigationMenuLink asChild>
                        <Link
                          href="/features/advanced/feature-4"
                          className="hover:text-primary block py-1 text-sm hover:underline"
                        >
                          Feature 4
                        </Link>
                      </NavigationMenuLink>
                    </li>
                  </ul>
                </div>

                <div className="min-w-[200px] space-y-3">
                  <Link
                    href="/features/upcoming"
                    className="text-foreground hover:text-primary mb-2 block border-b pb-2 text-lg font-medium whitespace-nowrap"
                  >
                    Upcoming Features
                  </Link>
                  <ul className="space-y-1">
                    <li>
                      <NavigationMenuLink asChild>
                        <Link
                          href="/features/upcoming/feature-5"
                          className="hover:text-primary block py-1 text-sm hover:underline"
                        >
                          Feature 5
                        </Link>
                      </NavigationMenuLink>
                    </li>
                    <li>
                      <NavigationMenuLink asChild>
                        <Link
                          href="/features/upcoming/feature-6"
                          className="hover:text-primary block py-1 text-sm hover:underline"
                        >
                          Feature 6
                        </Link>
                      </NavigationMenuLink>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </NavigationMenuContent>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  )
}

// Mobile navigation items
export function MobileNavigationItems({ onClose }: { onClose: () => void }) {
  const t = useTranslations()
  const [expandedCategories, setExpandedCategories] = React.useState({
    services: false,
    features: false,
  })

  const toggleCategory = (category: 'services' | 'features') => {
    setExpandedCategories((prev) => ({
      ...prev,
      [category]: !prev[category],
    }))
  }

  return (
    <div className="mt-2 flex flex-col space-y-2">
      {/* Main Menu Items */}
      <SheetClose asChild>
        <Link
          href={`/`}
          className="hover:bg-accent rounded-md px-3 py-2 font-medium"
          onClick={onClose}
        >
          {t('nav.home')}
        </Link>
      </SheetClose>

      <SheetClose asChild>
        <Link
          href={`/tools`}
          className="hover:bg-accent rounded-md px-3 py-2 font-medium"
          onClick={onClose}
        >
          {t('nav.tools')}
        </Link>
      </SheetClose>

      {/* Our Services Section - Collapsible */}
      <div className="border-border/50 border-t pt-2">
        <button
          className="hover:bg-accent flex w-full items-center justify-between rounded-md px-3 py-2 font-medium"
          onClick={() => toggleCategory('services')}
        >
          <span>Our Services</span>
          {expandedCategories.services ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </button>

        {expandedCategories.services && (
          <div className="border-border/50 mt-1 ml-2 flex flex-col space-y-1 border-l pl-2">
            {TOOL_CATEGORIES.map((category) => {
              // Get tools for this category
              const categoryTools = TOOLS.filter(
                (tool) => tool.category === category.slug
              )

              return (
                <div key={category.slug} className="py-1">
                  <SheetClose asChild>
                    <Link
                      href={`/tools#${category.slug}`}
                      className="hover:bg-accent block rounded-md px-3 py-1.5 font-medium"
                      onClick={onClose}
                    >
                      {category.name}
                    </Link>
                  </SheetClose>

                  <div className="border-border/50 ml-3 space-y-1 border-l pl-2">
                    {categoryTools.map((tool) => (
                      <SheetClose key={tool.slug} asChild>
                        <Link
                          href={`/tools/${tool.slug}`}
                          className="text-muted-foreground hover:text-foreground hover:bg-accent block rounded-md px-3 py-1 text-sm"
                          onClick={onClose}
                        >
                          {tool.name}
                        </Link>
                      </SheetClose>
                    ))}
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* Features Section - Collapsible */}
      <div className="border-border/50 border-t pt-2">
        <button
          className="hover:bg-accent flex w-full items-center justify-between rounded-md px-3 py-2 font-medium"
          onClick={() => toggleCategory('features')}
        >
          <span>Features</span>
          {expandedCategories.features ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </button>

        {expandedCategories.features && (
          <div className="border-border/50 mt-1 ml-2 flex flex-col space-y-1 border-l pl-2">
            <div className="py-1">
              <SheetClose asChild>
                <Link
                  href="/features"
                  className="hover:bg-accent block rounded-md px-3 py-1.5 font-medium"
                  onClick={onClose}
                >
                  Main Features
                </Link>
              </SheetClose>

              <div className="border-border/50 ml-3 space-y-1 border-l pl-2">
                <SheetClose asChild>
                  <Link
                    href="/features/feature-1"
                    className="text-muted-foreground hover:text-foreground hover:bg-accent block rounded-md px-3 py-1 text-sm"
                    onClick={onClose}
                  >
                    Feature 1
                  </Link>
                </SheetClose>
                <SheetClose asChild>
                  <Link
                    href="/features/feature-2"
                    className="text-muted-foreground hover:text-foreground hover:bg-accent block rounded-md px-3 py-1 text-sm"
                    onClick={onClose}
                  >
                    Feature 2
                  </Link>
                </SheetClose>
              </div>
            </div>

            <div className="py-1">
              <SheetClose asChild>
                <Link
                  href="/features/advanced"
                  className="hover:bg-accent block rounded-md px-3 py-1.5 font-medium"
                  onClick={onClose}
                >
                  Advanced Features
                </Link>
              </SheetClose>

              <div className="border-border/50 ml-3 space-y-1 border-l pl-2">
                <SheetClose asChild>
                  <Link
                    href="/features/advanced/feature-3"
                    className="text-muted-foreground hover:text-foreground hover:bg-accent block rounded-md px-3 py-1 text-sm"
                    onClick={onClose}
                  >
                    Feature 3
                  </Link>
                </SheetClose>
                <SheetClose asChild>
                  <Link
                    href="/features/advanced/feature-4"
                    className="text-muted-foreground hover:text-foreground hover:bg-accent block rounded-md px-3 py-1 text-sm"
                    onClick={onClose}
                  >
                    Feature 4
                  </Link>
                </SheetClose>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
