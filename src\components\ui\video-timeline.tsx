'use client'

import * as React from 'react'
import { cn } from '@/lib/utils'
import { formatTime } from '@/lib/video-utils'
import { RangeSlider } from './range-slider'

interface VideoTimelineProps {
  duration: number
  thumbnails: string[]
  value: [number, number]
  onChange: (value: [number, number]) => void
  currentTime?: number
  onSeek?: (time: number) => void
  className?: string
  disabled?: boolean
}

export function VideoTimeline({
  duration,
  thumbnails,
  value,
  onChange,
  currentTime = 0,
  onSeek,
  className,
  disabled = false,
}: VideoTimelineProps) {
  const timelineRef = React.useRef<HTMLDivElement>(null)

  const handleTimelineClick = (e: React.MouseEvent) => {
    if (!timelineRef.current || disabled || !onSeek) return

    const rect = timelineRef.current.getBoundingClientRect()
    const percentage = (e.clientX - rect.left) / rect.width
    const time = percentage * duration
    onSeek(Math.max(0, Math.min(duration, time)))
  }

  const currentTimePercentage = (currentTime / duration) * 100

  return (
    <div className={cn('space-y-4', className)}>
      {/* Thumbnails */}
      <div
        ref={timelineRef}
        className={cn(
          'relative flex h-16 w-full cursor-pointer overflow-hidden rounded-lg border',
          disabled && 'cursor-not-allowed opacity-50'
        )}
        onClick={handleTimelineClick}
      >
        {thumbnails.map((thumbnail, index) => (
          <div
            key={index}
            className="relative flex-1"
            style={{ width: `${100 / thumbnails.length}%` }}
          >
            <img
              src={thumbnail}
              alt={`Thumbnail ${index + 1}`}
              className="h-full w-full object-cover"
              draggable={false}
            />
          </div>
        ))}
        
        {/* Current time indicator */}
        {onSeek && (
          <div
            className="bg-primary absolute top-0 h-full w-0.5"
            style={{ left: `${currentTimePercentage}%` }}
          />
        )}
        
        {/* Selection overlay */}
        <div className="absolute inset-0">
          {/* Before selection */}
          <div
            className="absolute inset-y-0 left-0 bg-black/50"
            style={{ width: `${(value[0] / duration) * 100}%` }}
          />
          
          {/* After selection */}
          <div
            className="absolute inset-y-0 right-0 bg-black/50"
            style={{ width: `${100 - (value[1] / duration) * 100}%` }}
          />
          
          {/* Selection borders */}
          <div
            className="bg-primary absolute inset-y-0 w-0.5"
            style={{ left: `${(value[0] / duration) * 100}%` }}
          />
          <div
            className="bg-primary absolute inset-y-0 w-0.5"
            style={{ left: `${(value[1] / duration) * 100}%` }}
          />
        </div>
      </div>

      {/* Range Slider */}
      <div className="space-y-2">
        <RangeSlider
          min={0}
          max={duration}
          step={0.1}
          value={value}
          onChange={onChange}
          disabled={disabled}
        />
        
        {/* Time labels */}
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>{formatTime(value[0])}</span>
          <span>
            Duration: {formatTime(value[1] - value[0])}
          </span>
          <span>{formatTime(value[1])}</span>
        </div>
      </div>
    </div>
  )
}
