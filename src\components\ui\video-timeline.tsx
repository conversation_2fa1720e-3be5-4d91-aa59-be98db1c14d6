'use client'

import * as React from 'react'
import { cn } from '@/lib/utils'
import { formatTime, parseTime } from '@/lib/video-utils'

interface TimeInputProps {
  value: number
  onChange: (value: number) => void
  min?: number
  max?: number
  disabled?: boolean
}

function TimeInput({ value, onChange, min = 0, max = Infinity, disabled = false }: TimeInputProps) {
  const [inputValue, setInputValue] = React.useState(formatTime(value))
  const [isEditing, setIsEditing] = React.useState(false)

  React.useEffect(() => {
    if (!isEditing) {
      setInputValue(formatTime(value))
    }
  }, [value, isEditing])

  const handleFocus = () => {
    setIsEditing(true)
  }

  const handleBlur = () => {
    setIsEditing(false)
    try {
      const newTime = parseTime(inputValue)
      const clampedTime = Math.max(min, Math.min(max, newTime))
      onChange(clampedTime)
      setInputValue(formatTime(clampedTime))
    } catch {
      // Reset to current value if parsing fails
      setInputValue(formatTime(value))
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.currentTarget.blur()
    }
  }

  return (
    <input
      type="text"
      value={inputValue}
      onChange={(e) => setInputValue(e.target.value)}
      onFocus={handleFocus}
      onBlur={handleBlur}
      onKeyDown={handleKeyDown}
      disabled={disabled}
      className={cn(
        'w-16 px-2 py-1 text-xs font-mono text-center',
        'border border-border rounded',
        'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        'bg-background'
      )}
      placeholder="00:00"
    />
  )
}

interface VideoTimelineProps {
  duration: number
  thumbnails: string[]
  value: [number, number]
  onChange: (value: [number, number]) => void
  currentTime?: number
  onSeek?: (time: number) => void
  className?: string
  disabled?: boolean
}

export function VideoTimeline({
  duration,
  thumbnails,
  value,
  onChange,
  currentTime = 0,
  onSeek,
  className,
  disabled = false,
}: VideoTimelineProps) {
  const timelineRef = React.useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = React.useState<'start' | 'end' | 'playhead' | null>(null)
  const [dragOffset, setDragOffset] = React.useState(0)

  const handleTimelineClick = (e: React.MouseEvent) => {
    if (!timelineRef.current || disabled || !onSeek || isDragging) return

    const rect = timelineRef.current.getBoundingClientRect()
    const percentage = (e.clientX - rect.left) / rect.width
    const time = percentage * duration
    onSeek(Math.max(0, Math.min(duration, time)))
  }

  const handleMouseDown = (type: 'start' | 'end' | 'playhead', e: React.MouseEvent) => {
    if (disabled) return
    e.preventDefault()
    e.stopPropagation()

    if (!timelineRef.current) return

    const rect = timelineRef.current.getBoundingClientRect()
    const clickX = e.clientX - rect.left

    if (type === 'start') {
      setDragOffset(clickX - (value[0] / duration) * rect.width)
    } else if (type === 'end') {
      setDragOffset(clickX - (value[1] / duration) * rect.width)
    } else if (type === 'playhead') {
      setDragOffset(clickX - (currentTime / duration) * rect.width)
    }

    setIsDragging(type)
  }

  const handleMouseMove = React.useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !timelineRef.current || disabled) return

      const rect = timelineRef.current.getBoundingClientRect()
      const x = e.clientX - rect.left - dragOffset
      const percentage = Math.max(0, Math.min(1, x / rect.width))
      const time = percentage * duration

      if (isDragging === 'start') {
        const newStart = Math.max(0, Math.min(time, value[1] - 0.1))
        onChange([newStart, value[1]])
      } else if (isDragging === 'end') {
        const newEnd = Math.max(value[0] + 0.1, Math.min(time, duration))
        onChange([value[0], newEnd])
      } else if (isDragging === 'playhead' && onSeek) {
        onSeek(Math.max(0, Math.min(duration, time)))
      }
    },
    [isDragging, duration, value, onChange, onSeek, disabled, dragOffset]
  )

  const handleMouseUp = React.useCallback(() => {
    setIsDragging(null)
    setDragOffset(0)
  }, [])

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)

      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  const currentTimePercentage = (currentTime / duration) * 100
  const startPercentage = (value[0] / duration) * 100
  const endPercentage = (value[1] / duration) * 100

  return (
    <div className={cn('space-y-4', className)}>
      {/* Enhanced Timeline with Integrated Controls */}
      <div className="space-y-2">
        {/* Main Timeline */}
        <div
          ref={timelineRef}
          className={cn(
            'relative flex h-20 w-full cursor-pointer overflow-hidden rounded-lg border-2 border-border',
            disabled && 'cursor-not-allowed opacity-50'
          )}
          onClick={handleTimelineClick}
        >
          {/* Thumbnails */}
          {thumbnails.map((thumbnail, index) => (
            <div
              key={index}
              className="relative flex-1"
              style={{ width: `${100 / thumbnails.length}%` }}
            >
              <img
                src={thumbnail}
                alt={`Thumbnail ${index + 1}`}
                className="h-full w-full object-cover"
                draggable={false}
              />
            </div>
          ))}

          {/* Selection overlay */}
          <div className="absolute inset-0 pointer-events-none">
            {/* Before selection - dimmed */}
            <div
              className="absolute inset-y-0 left-0 bg-black/60"
              style={{ width: `${startPercentage}%` }}
            />

            {/* After selection - dimmed */}
            <div
              className="absolute inset-y-0 right-0 bg-black/60"
              style={{ width: `${100 - endPercentage}%` }}
            />

            {/* Selected area border */}
            <div
              className="absolute inset-y-0 border-2 border-blue-500 bg-blue-500/10"
              style={{
                left: `${startPercentage}%`,
                width: `${endPercentage - startPercentage}%`
              }}
            />
          </div>

          {/* Draggable Handles */}
          {/* Start Handle */}
          <div
            className={cn(
              'absolute top-0 h-full w-3 cursor-ew-resize pointer-events-auto',
              'bg-blue-500 hover:bg-blue-600 transition-colors',
              'flex items-center justify-center',
              isDragging === 'start' && 'bg-blue-600 scale-110'
            )}
            style={{ left: `calc(${startPercentage}% - 6px)` }}
            onMouseDown={(e) => handleMouseDown('start', e)}
          >
            <div className="w-1 h-8 bg-white rounded-full opacity-80" />
          </div>

          {/* End Handle */}
          <div
            className={cn(
              'absolute top-0 h-full w-3 cursor-ew-resize pointer-events-auto',
              'bg-blue-500 hover:bg-blue-600 transition-colors',
              'flex items-center justify-center',
              isDragging === 'end' && 'bg-blue-600 scale-110'
            )}
            style={{ left: `calc(${endPercentage}% - 6px)` }}
            onMouseDown={(e) => handleMouseDown('end', e)}
          >
            <div className="w-1 h-8 bg-white rounded-full opacity-80" />
          </div>

          {/* Current time playhead */}
          {onSeek && (
            <div
              className={cn(
                'absolute top-0 h-full w-0.5 bg-red-500 pointer-events-auto cursor-ew-resize',
                'before:absolute before:-left-2 before:top-0 before:w-5 before:h-5',
                'before:bg-red-500 before:rounded-full before:border-2 before:border-white',
                isDragging === 'playhead' && 'before:scale-125'
              )}
              style={{ left: `${currentTimePercentage}%` }}
              onMouseDown={(e) => handleMouseDown('playhead', e)}
            />
          )}
        </div>

        {/* Time Display and Input Controls */}
        <div className="flex justify-between items-center text-sm">
          <div className="flex items-center space-x-6">
            {/* Start Time Input */}
            <div className="flex items-center space-x-2">
              <label className="text-muted-foreground font-medium">Start:</label>
              <TimeInput
                value={value[0]}
                onChange={(newTime) => {
                  const clampedTime = Math.max(0, Math.min(newTime, value[1] - 0.1))
                  onChange([clampedTime, value[1]])
                }}
                max={value[1] - 0.1}
                disabled={disabled}
              />
            </div>

            {/* End Time Input */}
            <div className="flex items-center space-x-2">
              <label className="text-muted-foreground font-medium">End:</label>
              <TimeInput
                value={value[1]}
                onChange={(newTime) => {
                  const clampedTime = Math.max(value[0] + 0.1, Math.min(newTime, duration))
                  onChange([value[0], clampedTime])
                }}
                min={value[0] + 0.1}
                max={duration}
                disabled={disabled}
              />
            </div>

            {/* Duration Display */}
            <div className="flex items-center space-x-2">
              <span className="text-muted-foreground font-medium">Duration:</span>
              <span className="font-medium text-blue-600">
                {formatTime(value[1] - value[0])}
              </span>
            </div>
          </div>

          {onSeek && (
            <div className="flex items-center space-x-2">
              <span className="text-muted-foreground font-medium">Current:</span>
              <span className="font-mono text-sm">
                {formatTime(currentTime)}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
