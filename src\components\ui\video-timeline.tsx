'use client'

import * as React from 'react'
import { cn } from '@/lib/utils'
import { formatTime } from '@/lib/video-utils'

interface VideoTimelineProps {
  duration: number
  thumbnails: string[]
  value: [number, number]
  onChange: (value: [number, number]) => void
  currentTime?: number
  onSeek?: (time: number) => void
  className?: string
  disabled?: boolean
}

export function VideoTimeline({
  duration,
  thumbnails,
  value,
  onChange,
  currentTime = 0,
  onSeek,
  className,
  disabled = false,
}: VideoTimelineProps) {
  const timelineRef = React.useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = React.useState<'start' | 'end' | 'playhead' | null>(null)
  const [dragOffset, setDragOffset] = React.useState(0)

  const handleTimelineClick = (e: React.MouseEvent) => {
    if (!timelineRef.current || disabled || !onSeek || isDragging) return

    const rect = timelineRef.current.getBoundingClientRect()
    const percentage = (e.clientX - rect.left) / rect.width
    const time = percentage * duration
    onSeek(Math.max(0, Math.min(duration, time)))
  }

  const handleMouseDown = (type: 'start' | 'end' | 'playhead', e: React.MouseEvent) => {
    if (disabled) return
    e.preventDefault()
    e.stopPropagation()

    if (!timelineRef.current) return

    const rect = timelineRef.current.getBoundingClientRect()
    const clickX = e.clientX - rect.left

    if (type === 'start') {
      setDragOffset(clickX - (value[0] / duration) * rect.width)
    } else if (type === 'end') {
      setDragOffset(clickX - (value[1] / duration) * rect.width)
    } else if (type === 'playhead') {
      setDragOffset(clickX - (currentTime / duration) * rect.width)
    }

    setIsDragging(type)
  }

  const handleMouseMove = React.useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !timelineRef.current || disabled) return

      const rect = timelineRef.current.getBoundingClientRect()
      const x = e.clientX - rect.left - dragOffset
      const percentage = Math.max(0, Math.min(1, x / rect.width))
      const time = percentage * duration

      if (isDragging === 'start') {
        const newStart = Math.max(0, Math.min(time, value[1] - 0.1))
        onChange([newStart, value[1]])
      } else if (isDragging === 'end') {
        const newEnd = Math.max(value[0] + 0.1, Math.min(time, duration))
        onChange([value[0], newEnd])
      } else if (isDragging === 'playhead' && onSeek) {
        onSeek(Math.max(0, Math.min(duration, time)))
      }
    },
    [isDragging, duration, value, onChange, onSeek, disabled, dragOffset]
  )

  const handleMouseUp = React.useCallback(() => {
    setIsDragging(null)
    setDragOffset(0)
  }, [])

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)

      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  const currentTimePercentage = (currentTime / duration) * 100
  const startPercentage = (value[0] / duration) * 100
  const endPercentage = (value[1] / duration) * 100

  return (
    <div className={cn('space-y-4', className)}>
      {/* Enhanced Timeline with Integrated Controls */}
      <div className="space-y-2">
        {/* Main Timeline */}
        <div
          ref={timelineRef}
          className={cn(
            'relative flex h-20 w-full cursor-pointer overflow-hidden rounded-lg border-2 border-border',
            disabled && 'cursor-not-allowed opacity-50'
          )}
          onClick={handleTimelineClick}
        >
          {/* Thumbnails */}
          {thumbnails.map((thumbnail, index) => (
            <div
              key={index}
              className="relative flex-1"
              style={{ width: `${100 / thumbnails.length}%` }}
            >
              <img
                src={thumbnail}
                alt={`Thumbnail ${index + 1}`}
                className="h-full w-full object-cover"
                draggable={false}
              />
            </div>
          ))}

          {/* Selection overlay */}
          <div className="absolute inset-0 pointer-events-none">
            {/* Before selection - dimmed */}
            <div
              className="absolute inset-y-0 left-0 bg-black/60"
              style={{ width: `${startPercentage}%` }}
            />

            {/* After selection - dimmed */}
            <div
              className="absolute inset-y-0 right-0 bg-black/60"
              style={{ width: `${100 - endPercentage}%` }}
            />

            {/* Selected area border */}
            <div
              className="absolute inset-y-0 border-2 border-blue-500 bg-blue-500/10"
              style={{
                left: `${startPercentage}%`,
                width: `${endPercentage - startPercentage}%`
              }}
            />
          </div>

          {/* Draggable Handles */}
          {/* Start Handle */}
          <div
            className={cn(
              'absolute top-0 h-full w-3 cursor-ew-resize pointer-events-auto',
              'bg-blue-500 hover:bg-blue-600 transition-colors',
              'flex items-center justify-center',
              isDragging === 'start' && 'bg-blue-600 scale-110'
            )}
            style={{ left: `calc(${startPercentage}% - 6px)` }}
            onMouseDown={(e) => handleMouseDown('start', e)}
          >
            <div className="w-1 h-8 bg-white rounded-full opacity-80" />
          </div>

          {/* End Handle */}
          <div
            className={cn(
              'absolute top-0 h-full w-3 cursor-ew-resize pointer-events-auto',
              'bg-blue-500 hover:bg-blue-600 transition-colors',
              'flex items-center justify-center',
              isDragging === 'end' && 'bg-blue-600 scale-110'
            )}
            style={{ left: `calc(${endPercentage}% - 6px)` }}
            onMouseDown={(e) => handleMouseDown('end', e)}
          >
            <div className="w-1 h-8 bg-white rounded-full opacity-80" />
          </div>

          {/* Current time playhead */}
          {onSeek && (
            <div
              className={cn(
                'absolute top-0 h-full w-0.5 bg-red-500 pointer-events-auto cursor-ew-resize',
                'before:absolute before:-left-2 before:top-0 before:w-5 before:h-5',
                'before:bg-red-500 before:rounded-full before:border-2 before:border-white',
                isDragging === 'playhead' && 'before:scale-125'
              )}
              style={{ left: `${currentTimePercentage}%` }}
              onMouseDown={(e) => handleMouseDown('playhead', e)}
            />
          )}
        </div>

        {/* Time Display */}
        <div className="flex justify-between items-center text-sm">
          <div className="flex items-center space-x-4">
            <span className="text-muted-foreground">Start: {formatTime(value[0])}</span>
            <span className="text-muted-foreground">End: {formatTime(value[1])}</span>
            <span className="font-medium text-blue-600">
              Duration: {formatTime(value[1] - value[0])}
            </span>
          </div>
          {onSeek && (
            <span className="text-muted-foreground">
              Current: {formatTime(currentTime)}
            </span>
          )}
        </div>

        {/* Timeline Scale */}
        <div className="relative h-4">
          <div className="absolute inset-0 flex justify-between text-xs text-muted-foreground">
            {Array.from({ length: 11 }, (_, i) => (
              <div key={i} className="flex flex-col items-center">
                <div className="w-px h-2 bg-muted-foreground/30" />
                <span className="mt-1">{formatTime((duration * i) / 10)}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
